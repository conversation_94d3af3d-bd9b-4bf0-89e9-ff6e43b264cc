using Microsoft.EntityFrameworkCore;

namespace Diploma.Data
{
    public partial class DiplomaReadDataContext : DbContext
    {
        public DiplomaReadDataContext()
        {
        }

        public DiplomaReadDataContext(DbContextOptions<DiplomaReadDataContext> options)
            : base(options)
        {
        }

        public virtual DbSet<KhBacDaoTao> KhBacDaoTaos { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
