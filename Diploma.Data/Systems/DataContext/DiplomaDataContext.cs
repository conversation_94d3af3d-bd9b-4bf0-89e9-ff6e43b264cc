using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Threading;
using System;
using Core.Shared.ContextAccessor;
using Core.Shared;

namespace Diploma.Data
{
    public partial class DiplomaDataContext : DbContext
    {
        private readonly IContextAccessor _contextAccessor;

        public DiplomaDataContext(Func<IContextAccessor> contextAccessorFactory)
        {
            _contextAccessor = contextAccessorFactory();
        }

        public DiplomaDataContext(DbContextOptions<DiplomaDataContext> options, Func<IContextAccessor> contextAccessorFactory)
            : base(options)
        {
            _contextAccessor = contextAccessorFactory();
        }

        public virtual DbSet<KhBacDaoTao> KhBacDaoTaos { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);

        public override int SaveChanges()
        {
            return SaveChangesAsync().Result;
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            var entries = ChangeTracker.Entries();
            foreach (var entry in entries)
            {
                if (entry.Entity is TrackedChangeEntity entity)
                {
                    switch (entry.State)
                    {
                        case EntityState.Added:
                            entity.CreatedDate = DateTime.Now;
                            entity.CreatedUserId = _contextAccessor.UserId;
                            entity.CreatedUserName = _contextAccessor.UserName;
                            break;
                        case EntityState.Modified:
                            entity.ModifiedDate = DateTime.Now;
                            entity.ModifiedUserId = _contextAccessor.UserId;
                            entity.ModifiedUserName = _contextAccessor.UserName;
                            break;
                    }
                }
            }
            return base.SaveChangesAsync(cancellationToken);
        }
    }
}
