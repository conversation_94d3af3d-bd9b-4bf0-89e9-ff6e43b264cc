using Core.Shared;
using System.ComponentModel.DataAnnotations.Schema;

namespace Diploma.Data
{
    [Table("khBacDaoTao")]
    public class KhBacDaoTao : BaseEntity
    {
        public KhBacDaoTao()
        {
        }

        /// <summary>
        /// Mã loại đề tài
        /// </summary>
        [Column("code")]
        public string Code { get; set; }

        /// <summary>
        /// Tên loại đề tài
        /// </summary>
        [Column("name")]
        public string Name { get; set; }
    }
}
