using Core.Shared;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Diploma.Shared
{
    public enum PermissionDiplomaEnum
    {
        #region Bậc đào tạo khoa học
        [Display(GroupName = "Bậc đào tạo khoa học", Name = "Xem thông tin bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_VIEW,

        [Display(GroupName = "Bậc đào tạo khoa học", Name = "Thêm mới bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_CREATE,

        [Display(GroupName = "Bậc đào tạo khoa học", Name = "Cập nhật bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_UPDATE,

        [Display(GroupName = "Bậc đào tạo khoa học", Name = "<PERSON><PERSON><PERSON> bậc đào tạo khoa học")]
        KH_BAC_DAO_TAO_DELETE,
        #endregion
    }
}