using System.Reflection.Metadata;

namespace Diploma.Shared
{
    /// <summary>
    /// Cachprefix - <PERSON><PERSON><PERSON> ý không được viết hoa vì elastic không nhận chữ hoa trong index
    /// </summary>
    public class DiplomaCacheConstants
    {
        public const string KH_BAC_DAO_TAO = "SciKhBacDaoTao";
    }

    public class DiplomaLogConstants
    {
        #region Bậc đào tạo khoa học
        public const string ACTION_KH_BAC_DAO_TAO_CREATE = "Thêm mới bậc đào tạo khoa học";
        public const string ACTION_KH_BAC_DAO_TAO_UPDATE = "Cập nhật bậc đào tạo khoa học";
        public const string ACTION_KH_BAC_DAO_TAO_DELETE = "Xóa bậc đào tạo khoa học";
        #endregion
     
    }

    public class DiplomaThamSoHeThongConstants
    {
       
    }

    public class Actions
    {
        public const string ADD = "add";
        public const string EDIT = "edit";
        public const string DEL = "delete";
    }
}
