using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Core.Shared;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Core.API.Shared;
using Core.Business.Service.CheckConnectionService;
using UAParser;
using System.Linq;
using Core.Shared.ContextAccessor;
using System;
using Core.Business.System.Pdf;
using Core.Shared.Model;
using System.IO;
using System.Collections.Generic;
using Core.Business;
using Microsoft.AspNetCore.Hosting;
using Serilog;

namespace Core.API.Controller
{
    /// <summary>
    /// Module test redis
    /// </summary>
    [ApiController]
    [Route("system/v1/test")]
    [ApiExplorerSettings(GroupName = "00. Test", IgnoreApi = false)]
    [AllowAnonymous]
    public class TestController : ApiControllerBaseV2
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        public TestController(
            IWebHostEnvironment webHostEnvironment,
            Func<IContextAccessor> contextAccessorFactory,
            IMediator mediator,
            IStringLocalizer<Resources> localizer,
            IConfiguration config) : base(contextAccessorFactory, mediator, localizer, config)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Test Connection
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("connection")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> ConnectionCheckQuery()
        {
            return await ExecuteFunction(async () =>
            {
                return await _mediator.Send(new ConnectionCheckQuery());
            });
        }

        /// <summary>
        /// Get Client info
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("get-client-info")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetClientInfo()
        {
            return await ExecuteFunction(async () =>
            {
                string clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                var ip = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
                var realIp = HttpContext.Request.Headers["X-Real-Ip"].FirstOrDefault();
                string userAgent = HttpContext.Request.Headers["User-Agent"].ToString();
                string requestMethod = HttpContext.Request.Method;
                string requestPath = HttpContext.Request.Path;

                var parser = Parser.GetDefault();
                var clientInfo = parser.Parse(userAgent);

                // get all header
                var headers = HttpContext.Request.Headers;

                var rs = new
                {
                    ClientIP = Helper.GetIPAddress(HttpContext.Request),
                    RealIp = realIp ?? "",
                    ForwardedForIP = ip ?? "",
                    RemoteIpAddress = clientIp,
                    UserAgent = userAgent,
                    RequestMethod = requestMethod,
                    RequestPath = requestPath,
                    Os = clientInfo.OS.ToString(),
                    Browser = clientInfo.UA.ToString(),
                    ClientInfo = clientInfo.ToString(),
                    headers = headers,
                };

                return rs;
            });
        }

        /// <summary>
        /// Docx to PDF from Base64
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-base64")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDF([FromBody] PdfConvertFromWordBase64Model request)
        {
            return await ExecuteFunction(async () =>
            {
                // Đọc file từ thư mục và convert về base64
                return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));
            });
        }

        /// <summary>
        /// Docx to PDF
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-file-upload")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDFFromFileUpload(IFormFile file)
        {
            return await ExecuteFunction(async () =>
            {
                if (file == null || file.Length == 0)
                    throw new ArgumentException("File is null");

                // Lấy định dạng file
                var fileExtension = Path.GetExtension(file.FileName).ToLower();

                // Kiểm tra định dạng file
                var allowedExtensions = new List<string>() { ".docx" };
                if (!allowedExtensions.Contains(fileExtension))
                {
                    throw new ArgumentException("Invalid file format");
                }

                using (var memoryStream = new MemoryStream())
                {
                    await file.CopyToAsync(memoryStream);
                    byte[] fileBytes = memoryStream.ToArray();

                    PdfConvertFromWordBase64Model request = new PdfConvertFromWordBase64Model
                    {
                        FileName = file.FileName,
                        FileBase64 = Convert.ToBase64String(fileBytes),
                        Metadatas = new List<KeyValueModel>()
                    };

                    // Đọc file từ thư mục và convert về base64
                    return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));
                }
            });
        }

        /// <summary>
        /// Docx to PDF
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-template")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDFFromFileTemplate()
        {
            return await ExecuteFunction(async () =>
            {
                var filePath = "templates/template-word.docx"; // Đường dẫn đến tệp mẫu
                var fileName = Path.GetFileName(filePath).ToLower();
                var fullFilePath = Path.Combine(_webHostEnvironment.WebRootPath, filePath);

                if (!System.IO.File.Exists(fullFilePath))
                    throw new ArgumentException("File not found");

                byte[] fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath);

                PdfConvertFromWordBase64Model request = new PdfConvertFromWordBase64Model
                {
                    FileName = fileName,
                    FileBase64 = Convert.ToBase64String(fileBytes),
                    Metadatas = new List<KeyValueModel>()
                };

                // Đọc file từ thư mục và convert về base64
                return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));

            });
        }

        /// <summary>
        /// Docx to PDF
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("docx-to-pdf-from-template-with-table")]
        [ProducesResponseType(typeof(ResponseObject<FileBase64Response>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DocxToPDFFromFileTemplateWithTableData()
        {
            return await ExecuteFunction(async () =>
            {
                var filePath = "templates/template-word.docx"; // Đường dẫn đến tệp mẫu
                var fileName = Path.GetFileName(filePath).ToLower();
                var fullFilePath = Path.Combine(_webHostEnvironment.WebRootPath, filePath);

                if (!System.IO.File.Exists(fullFilePath))
                    throw new ArgumentException("File not found");

                byte[] fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath);

                PdfConvertFromWordBase64Model request = new PdfConvertFromWordBase64Model
                {
                    FileName = fileName,
                    FileBase64 = Convert.ToBase64String(fileBytes),
                    Metadatas = new List<KeyValueModel>() { 
                        new KeyValueModel() { Key = "{date}", Value = DateTime.Now.ToString() } ,
                        new KeyValueModel() { Key = "{documentName}", Value = "Document test DocxToPDFFromFileTemplateWithTableData" } 
                    },
                    TableDatas = new List<TableDataModel>
                    {
                        new TableDataModel
                        {
                            TableName = "{#Bang1#}",
                            TableData = new List<Dictionary<string, string>>()
                            {
                                new Dictionary<string, string>() { { "{index}", "1" }, { "{name}", "Nguyễn Văn A" }, { "{order}", "101" } },
                                new Dictionary<string, string>() { { "{index}", "2" }, { "{name}", "Trần Thị B" }, { "{order}", "102" } }
                            }
                        },
                        new TableDataModel
                        {
                            TableName = "{#Bang2#}",
                            TableData = new List<Dictionary<string, string>>()
                            {
                                new Dictionary<string, string>() { { "{index}", "1.1" }, { "{name}", "Nguyễn Văn A.1" }, { "{order}", "101.1" } },
                                new Dictionary<string, string>() { { "{index}", "2.1" }, { "{name}", "Trần Thị B.1" }, { "{order}", "102.1" } }
                            }
                        },
                        new TableDataModel
                        {
                            TableName = "{#Bang3#}",
                            TableData = new List<Dictionary<string, string>>()
                            {
                                new Dictionary<string, string>() { { "{index}", "1.12" }, { "{name}", "Nguyễn Văn A.12" }, { "{order}", "101.12" } },
                                new Dictionary<string, string>() { { "{index}", "2.12" }, { "{name}", "Trần Thị B.12" }, { "{order}", "102.12" } }
                            }
                        }
                    }
                };

                // Đọc file từ thư mục và convert về base64
                return await _mediator.Send(new ConvertPDFFromWordAndMetaDataCommand(request));
            });
        }
    }
}
