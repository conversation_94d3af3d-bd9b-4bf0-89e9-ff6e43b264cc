using MediatR;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Threading.Tasks;
using System.Threading;
using System;
using Microsoft.Extensions.DependencyInjection;
using Diploma.Business;

namespace Diploma.API.Services
{
    public class StartupService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;

        public StartupService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                Log.Information("StartupService");
                try
                {
                    // Nên chạy migration trước khi seed data
                    await scope.ServiceProvider
                        .GetRequiredService<IMediator>()
                        .Send(new RunDiplomaMigrationsCommand());

                    await scope.ServiceProvider
                        .GetRequiredService<IMediator>()
                        .Send(new SeedDataCommand(new Core.Shared.SystemLogModel()));
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error during startup service initialization");
                    // <PERSON><PERSON> thể throw lại hoặc xử lý tùy theo yêu cầu hệ thống
                }
            }
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
