using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Core.Shared;
using Swashbuckle.AspNetCore.SwaggerUI;
using System;
using System.Collections.Generic;
using System.Linq;
using Core.API.Shared;
using Core.Data;
using Microsoft.EntityFrameworkCore;
using Diploma.Data;
using Core.Shared.Middlewares;
using Prometheus;
using Microsoft.AspNetCore.HttpOverrides;
using OptimaJet.Workflow.Core.Runtime;
using Core.Business.Workflow.Implementation;
using Core.Business.Workflow;
using Core.Business.Workflow.Interface;

namespace Diploma.API
{
    public class Startup
    {
        public IConfiguration configuration { get; }

        public WorkflowRuntime Runtime { get; private set; }

        private const string DefaultCorsPolicyName = "Default";

        public Startup(IWebHostEnvironment env)
        {
            var builder = StartupHelpers.CreateDefaultConfigurationBuilder(env);

            if (env.IsDevelopment())
            {
                //builder.AddUserSecrets<Startup>();
            }
            configuration = builder.Build();

            // Lấy thông tin vault từ configuration
            var vaultOptions = configuration.GetSection("Vault").Get<VaultOptions>();
            if (vaultOptions?.Enabled == true)
            {
                // Thêm cấu hình của Vault vào Configuration
                configuration = new ConfigurationBuilder()
                    .AddConfiguration(configuration)
                    .AddVault(options =>
                    {
                        options.Address = vaultOptions.Address;
                        options.Token = vaultOptions.Token;
                        options.Mount = vaultOptions.Mount;
                        options.Secret = vaultOptions.Secret;
                        options.SecretDetail = vaultOptions.SecretDetail;
                    })
                    .Build();
            }
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        // For more information on how to configure your application, visit https://go.microsoft.com/fwlink/?LinkID=398940
        public void ConfigureServices(IServiceCollection services)
        {
            services.RegisterLogComponents(configuration);

            services.RegisterIConfigurationComponents(configuration);

            services.RegisterApplicationContextAccessor();

            services.RegisterApplicationContextAccessor();

            services.RegisterCacheComponents(configuration);

            #region Config database
            // System
            string systemDB = String.Format(configuration["Database:System:ConnectionString:MSSQLDatabase"], configuration["uid"], configuration["password"]);
            services.AddDbContext<SystemDataContext>(x =>
            {
                x.UseSqlServer(systemDB);
                x.EnableSensitiveDataLogging();
            });
            string systemDBRead = String.Format(configuration["Database:System:ConnectionString:MSSQLDatabaseRead"], configuration["uid"], configuration["password"]);
            services.AddDbContext<SystemReadDataContext>(x =>
            {
                x.UseSqlServer(systemDBRead);
                x.EnableSensitiveDataLogging();
            });

            // Diploma
            string diplomaDB = String.Format(configuration["Database:Diploma:ConnectionString:MSSQLDatabase"], configuration["uid"], configuration["password"]);
            services.AddDbContext<DiplomaDataContext>(x =>
            {
                x.UseSqlServer(diplomaDB);
                x.EnableSensitiveDataLogging();
            });
            string diplomaReadDB = String.Format(configuration["Database:Diploma:ConnectionString:MSSQLDatabaseRead"], configuration["uid"], configuration["password"]);
            services.AddDbContext<DiplomaReadDataContext>(x =>
            {
                x.UseSqlServer(diplomaReadDB);
                x.EnableSensitiveDataLogging();
            });
            #endregion Config database

            services.RegisterMongoDBDataContextServiceComponents(configuration);

            services.AddFluentEmail(configuration);

            services.RegisterCustomServiceComponents(configuration);

            services.RegisterHostedServiceComponents(configuration);

            services.AddMvcCore();

            services.AddOptions();

            services.AddCors(options =>
            {
                options.AddPolicy(DefaultCorsPolicyName, policy =>
                {
                    policy
                        .WithOrigins(
                            configuration["AppSettings:CorsOrigins"]
                                .Split(",", StringSplitOptions.RemoveEmptyEntries)
                                .Select(o => o.RemovePostFix("/"))
                                .ToArray()
                        )
                        .WithExposedHeaders("_UniCoreErrFormat")
                        .SetIsOriginAllowedToAllowWildcardSubdomains()
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
                });
            });

            services.AddControllers()
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.IgnoreNullValues = true;
                    options.JsonSerializerOptions.Converters.Add(new TimeSpanToStringConverter());
                });

            services.RegisterLocalizationServiceComponents();

            services.RegisterAPIVersionServiceComponents();

            services.AddCustomAuthenServiceComponents();

            services.RegisterSwaggerServiceComponents(configuration);

            services.RegisterCustomerModelParsingServiceComponents();

            if (configuration["redis:enabled"] == "true")
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = configuration["redis:configuration"];
                    options.InstanceName = configuration["redis:instanceName"];
                });
            }

            services.AddHealthChecks();

            services.AddSignalR();

            services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(assembly));
            }

            services.AddRouting();
            services.AddHttpClient();

            services.AddScoped<IPersistenceProviderContainer, PersistenceProviderContainer>();

            WorkflowInit.ServiceProvider = services.BuildServiceProvider();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            app.UseMetricServer();//Starting the metrics exporter, will expose "/metrics"

            app.UseMiddleware<HttpRequestMiddleware>();

            app.UseMiddleware<HttpRequestMiddleware>();

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            if (configuration["AppSettings:EnableSwagger"] == "true")
            {
                // Enable middleware to serve generated Swagger as a JSON endpoint.
                app.UseSwagger(c =>
                {
                    //c.SerializeAsV2 = true; //Swagger 2.0
                });

                // Enable middleware to serve swagger-ui (HTML, JS, CSS, etc.),
                // specifying the Swagger JSON endpoint.
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Net Core API V1");
                    c.DocExpansion(DocExpansion.None);
                    c.DisplayRequestDuration();
                    // To serve SwaggerUI at application's root page, set the RoutePrefix property to an empty string.
                    c.RoutePrefix = "";
                });
            }

            app.UseHttpsRedirection();

            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
            });

            #region Localizations
            var localizeOptions = app.ApplicationServices.GetService<IOptions<RequestLocalizationOptions>>();
            app.UseRequestLocalization(localizeOptions.Value);
            #endregion

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseCors(DefaultCorsPolicyName);

            app.UseDefaultFiles(new DefaultFilesOptions()
            {
                DefaultFileNames = new List<string>() { "index.html" },
                RequestPath = new PathString("")
            });
            app.UseStaticFiles();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            app.UseHealthChecks("/health");//your request URL will be health

            if (!string.IsNullOrEmpty(configuration.GetSection("Database:Workflow:ConnectionString:MSSQLDatabase")?.Value))
            {
                Runtime = WorkflowInit.Create(new PersistenceProviderContainer(configuration));
            }

            Spire.License.LicenseProvider.SetLicenseKey("CtOzJs2BlzPokWgBAKMfmNxjRwLa3eqzrAvKtn54UDB/dWjIyGokcs+UQuYuvMY03wX56Ox75KV+U1r5H0PR++c1zc6i8e0QIOVuhMp9Qbg5A9bJJA7e7KvC4KMINTr4jnJy/yTGFwT1aEusw144kml/6oAttwEUoXBkDPLWGOsvNgH1iTYkTGWMXEV8Or4p4t4doNsl0Z7V5qWDKwB6sD/ZiH7l/Jum27FWevOlKIa2VG1rEKjtURYukbWXeSH54IKtmn7nmr0wKwnRgdu3q60aC/PdkxC0zX75EnbU5M6fa3pplU40f3LGOWcgZ2f+8oI7qpPXJ8/s7LrsxBqpQ2YGKfKuqx5ex9ALrXgjnwjcslmXPYun7flHGIkbvBsCjCpo4Ed+M658sZTGATak6gLmftEqhJ1ZZJJKFgXE5qa/TyCY7wIq1ll+z1VNhnSBZUc1RA4TwSBcFKvrZEHlj9o1WFZ1+QqNAcnzh/n+tG48B0wHLCl6D4hroCfWMoaw/23DRxx1WuWqfkazuz2H8ga1RC2XPs83nB7CHPFNs0sT5lsKbfA3P9jgtza5CEhfjAN/3TiwEP/tvnTZY+VABK97veB77h4LEiVMfQXzKfhm9cNW4ft/ofVU2OfqZ8GjtntoZdPxp1bIwTvI98SnQi/H81w19aHwUqNECTeJBjqqHMxdVKVSBAKJL0TM7RyzoOPKS19OfURAxlEgRUqJF/BM8eU0R+UicIM2h36sTuBKO4g3H6woDMlnx0QG0nqthauTB7oK6QFTwk44UQ1kTAu8LeOJwM2xNu5MLsPmoWwDvmIaTuZIW6VUX8C285c9KkrYAf79YKA3e3yxx6SSQdN/jLbtR7MaeGpxRzX0iEbqL9sG1m5USuYVByvVKQ4ntvfCMlLmUN9UCvJ/m63K27Z2dm6fTXIe/g0smYmnvEQ3JQVnldWOi1TKOMK8RbuU5un5mQZ96pLq0Q7g0NLQZh50UMT+OjAzXHPxmXfV6/deHeE8Gbb3ZYJSg7UXW2sty86uXwkj89x5yJTaMNtm6Kh2QQugn/Vd9n8C8QReNewYxjF827FBpMp9yf+vLf2FSyA50wiA9o9luoXYgRmGuUh+g9+KMWgMK5fxQ2h3cHqADzPcwsDhVfG6HuAgt81vH/M5hFLdQztXdvRKVuYOyyTOnQz9K93LZ2EvbeWz0YByRkGxnve+K8UNo3pyNgaPGRQWr5RbeURNJ4PhmM3dB2oMkwE//+s39ccgADdEJS8s35cjRrVEGs8JicRu6mDNqJfdHUNfLmiySMjG/ePwhYkiB2WhJ9AqpY9N7eQ3TBsAMkr34olS6eSNpaE1BjgJsljB27GDnmMAXNZeifyIYpBcqu6H9SLN5pGBF9WHcPVivjdNpMUrKQ==");
        }
    }
}
