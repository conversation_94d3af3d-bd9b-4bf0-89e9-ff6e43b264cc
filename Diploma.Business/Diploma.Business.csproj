<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="lib\**" />
    <EmbeddedResource Remove="lib\**" />
    <None Remove="lib\**" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ExternalClients\HouHRMClient\HouHRMClient.csproj" />
    <ProjectReference Include="..\Diploma.Data\Diploma.Data.csproj" />
    <ProjectReference Include="..\Diploma.Shared\Diploma.Shared.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.Business\Core.Business.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.DataLog\Core.DataLog.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.Data\Core.Data.csproj" />
    <ProjectReference Include="..\uni-system-api\CoreService\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>
</Project>