using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Data;
using System.Data.SqlClient;

namespace Diploma.Business
{
    public class DiplomaCallStoreHelper : IDiplomaCallStoreHelper
    {
        private readonly IConfiguration _config;
        public DiplomaCallStoreHelper(IConfiguration config)
        {
            _config = config;
        }

        private string DatabaseConnectionString()
        {
            return String.Format(_config["Database:Diploma:ConnectionString:MSSQLDatabase"], _config["uid"], _config["password"]);
        }

        public DataTable CallStoreDanhSachLopAsync(string userName)
        {
            SqlCommand cmd = new SqlCommand();
            SqlDataAdapter da = new SqlDataAdapter();
            DataTable dt = new DataTable();
            SqlConnection connection = new SqlConnection(DatabaseConnectionString());
            try
            {
                cmd = new SqlCommand("sp_appTeacher_ListClass", connection);
                cmd.Parameters.Add(new SqlParameter("@UserName", userName));
                cmd.CommandType = CommandType.StoredProcedure;
                da.SelectCommand = cmd;
                da.Fill(dt);
                da.Update(dt);
                return dt;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "CallStore Error");
                throw;
            }
            finally
            {
                dt.Dispose();
                da.Dispose();
                cmd.Dispose();
                connection.Close();
            }
        }
    }
}