using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Diploma.Business.ThirdPartyBussiness.Hrm
{
    public class HrmEmployeeModel
    {
        public string ReferenceId { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class HrmEmployeeDetailModel
    {
        public string ReferenceId { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string Address { get; set; }
        public string Gender { get; set; }
        public string IdentityNumber { get; set; }
    }

    public class HrmPhongBanModel
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string TenVietTat { get; set; }
        public string DiaChi { get; set; }
        public string SoDienThoai { get; set; }
        public string SoFax { get; set; }
        public string Email { get; set; }
        public string Ghi<PERSON>hu { get; set; }
        public string CodePhongBanCha { get; set; }
        public string CodeDonVi { get; set; }
    }

    public class HrmHoSoCanBoModel
    {
        public string MaCanBo { get; set; }
        public string HoTen { get; set; }
        public string UrlAnh { get; set; }
        public string MaGioiTinh { get; set; }
        public DateTime? NgaySinh { get; set; }
        public string NoiSinh { get; set; }
        public string QueQuan { get; set; }
        public string MaDanToc { get; set; }
        public string DiaChiLienHe { get; set; }
        public string DienThoaiCaNhan { get; set; }
        public string Email { get; set; }
        public string CCCD { get; set; }
        public DateTime? NgayCapCCCD { get; set; }
        public string NoiCapCCCD { get; set; }
        public string MaSoThue { get; set; }
        public string MaHocVi { get; set; }
        public int? NamNhanHocVi { get; set; }
        public string MaChucDanh { get; set; }
        public int? NamNhanChucDanh { get; set; }
        public string ChuyenNganhNghienCuu { get; set; }
        public string MaChucVu { get; set; }
        public string DiaChiCoQuan { get; set; }
        public string DienThoaiCoQuan { get; set; }
        public string EmailCoQuan { get; set; }
        public string FaxCoQuan { get; set; }
        public string MaPhongBan { get; set; }
        public string TaiKhoanNganHang { get; set; }
        public string TenNganHang { get; set; }
        public string ChiNhanhNganHang { get; set; }

        public List<HrmQuaTrinhDaoTaoModel> QuaTrinhDaoTao { get; set; }
        public List<HrmNgoaiNguModel> NgoaiNgu { get; set; }
        public List<HrmDaoTaoNganHanModel> DaoTaoNganHan { get; set; }
        public List<HrmQuaTrinhCongTacModel> QuaTrinhCongTac { get; set; }
    }

    public class HrmQuaTrinhDaoTaoModel
    {
        public string MaBacDaoTao { get; set; }
        public string MaChuyenNganh { get; set; }
        public string NoiDaoTao { get; set; }
        public int? NamTotNghiep { get; set; }
        public string MaQuocGia { get; set; }
        public string TenLuanAn { get; set; }
        public string GhiChu { get; set; }
    }

    public class HrmNgoaiNguModel
    {
        public string TenNgoaiNgu { get; set; }
        public string TrinhDo { get; set; }
        public double? DiemNghe { get; set; }
        public double? DiemNoi { get; set; }
        public double? DiemDoc { get; set; }
        public double? DiemViet { get; set; }
        public string GhiChu { get; set; }
    }

    public class HrmDaoTaoNganHanModel
    {
        public string TenKhoaHoc { get; set; }
        public string NoiDungDaoTao { get; set; }
        public string DonViDaoTao { get; set; }
        public string ChungChi { get; set; }
        public DateTime? TuNgay { get; set; }
        public DateTime? DenNgay { get; set; }
        public string GhiChu { get; set; }
    }

    public class HrmQuaTrinhCongTacModel
    {
        public int? TuNam { get; set; }
        public int? DenNam { get; set; }
        public string NoiCongTac { get; set; }
        public string ChucVu { get; set; }
        public string CongViecDamNhan { get; set; }
        public string GhiChu { get; set; }
    }

}
