using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Business;
using Diploma.Shared;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Configuration;

namespace Diploma.Business
{
    public class SeedDataCommand : IRequest<Unit>
    {
        public SystemLogModel SystemLog { get; set; }

        public SeedDataCommand(SystemLogModel systemLog)
        {
            SystemLog = systemLog;
        }

        public class Handler : IRequestHandler<SeedDataCommand, Unit>
        {
            private readonly SystemDataContext _dataContext;
            private readonly IMediator _mediator;
            private readonly ICacheService _cacheService;
            private readonly IConfiguration _config;
            public Handler(SystemDataContext dataContext, IMediator mediator, ICacheService cacheService, IConfiguration config)
            {
                _dataContext = dataContext;
                _mediator = mediator;
                _cacheService = cacheService;
                _config = config;
            }

            public async Task<Unit> Handle(SeedDataCommand request, CancellationToken cancellationToken)
            {
                Log.Information($"Khởi tạo dữ liệu hệ thống");

                // UniDiploma
                var maPhanHe = "UniSci";
                int order = 0;

                var phanhe = await _dataContext.HtPhanHes.FirstOrDefaultAsync(x => x.PhanHe == maPhanHe);

                if (phanhe == null)
                {
                    phanhe = new HtPhanHe
                    {
                        PhanHe = maPhanHe,
                        MieuTa = "Phần mềm Quản lý hoạt động khoa học",
                        KyHieuPhanHe = "USD",
                        HinhAnhPhanHe = "/assets/tmp/img/app/UniSci.png",
                        Active = true,
                        STT = 1,
                        Hiden = false,
                    };
                    await _dataContext.HtPhanHes.AddAsync(phanhe);
                    Log.Information($"Thêm mới phân hệ {maPhanHe} vào hệ thống");
                }

                var listPermissionInDB = await _dataContext.Permissions.Where(x => x.IdPhanHe == phanhe.IdPh).ToListAsync();
                var listPermissionInConstant = new List<PermissionSeedModel>();

                foreach (PermissionDiplomaEnum permission in Enum.GetValues(typeof(PermissionDiplomaEnum)))
                {
                    FieldInfo field = permission.GetType().GetField(permission.ToString());
                    DisplayAttribute attribute = (DisplayAttribute)Attribute.GetCustomAttribute(field, typeof(DisplayAttribute));

                    string groupName = attribute.GroupName;
                    string name = attribute.Name;

                    listPermissionInConstant.Add(new PermissionSeedModel
                    {
                        Code = permission.ToString(),
                        Name = name,
                        GroupName = groupName,
                        Order = order++,
                        IsActive = true
                    });
                }
                var listPermissionNeedAdd = listPermissionInConstant.Where(x => !listPermissionInDB.Any(y => y.Code == x.Code)).ToList();
                if (listPermissionNeedAdd.Any())
                {
                    var listNeedAdd = AutoMapperUtils.AutoMap<PermissionSeedModel, Permission>(listPermissionNeedAdd);
                    foreach (var item in listNeedAdd)
                    {
                        item.IdPhanHe = phanhe.IdPh;
                        item.CreatedDate = DateTime.Now;
                    }
                    Log.Information($"Thêm mới {listNeedAdd.Count} quyền Diploma vào hệ thống");
                    await _dataContext.Permissions.AddRangeAsync(listNeedAdd);
                }

                #region Thêm mẫu email
                var listSeedEmail = DiplomaEmailTemplateSeedConstant.ListSeedEmails;
                var listEmailInDB = await _dataContext.EmailTemplates.ToListAsync();
                int countEmail = 0;
                listSeedEmail.ForEach(async item =>
                {
                    var emailTemplate = listEmailInDB.FirstOrDefault(x => x.Code == item.Code);
                    if (emailTemplate == null)
                    {
                        emailTemplate = new EmailTemplate
                        {
                            Code = item.Code,
                            Name = item.Name,
                            Subject = item.Subject,
                            Template = item.Template,
                            IsActive = true,
                            Order = countEmail++,
                            CreatedDate = DateTime.Now
                        };
                        await _dataContext.EmailTemplates.AddAsync(emailTemplate);
                    }
                });

                Log.Information($"Thêm mới {countEmail} mẫu email vào hệ thống");
                #endregion

                await _dataContext.SaveChangesAsync();
                Log.Information($"Khởi tạo dữ liệu thành công");

                //Xóa cache
                _cacheService.RemoveAll();
                return Unit.Value;
            }
        }
    }
}