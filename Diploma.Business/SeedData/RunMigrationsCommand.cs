using MediatR;
using Microsoft.EntityFrameworkCore;
using Core.Data;
using Core.Shared;
using Serilog;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Core.Business;
using Diploma.Shared;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Diploma.Data;
using Microsoft.Extensions.Logging;

namespace Diploma.Business
{
    public class RunDiplomaMigrationsCommand : IRequest<Unit>
    {
        public RunDiplomaMigrationsCommand()
        {
        }

        public class Handler : IRequestHandler<RunDiplomaMigrationsCommand, Unit>
        {
            private readonly DiplomaDataContext _dataContext;
            private readonly ILogger<RunDiplomaMigrationsCommand> _logger;
            private readonly IMediator _mediator;
            private readonly ICacheService _cacheService;
            private readonly IConfiguration _config;
            public Handler(DiplomaDataContext dataContext, IMediator mediator, ICacheService cacheService, IConfiguration config, ILogger<RunDiplomaMigrationsCommand> logger)
            {
                _dataContext = dataContext;
                _mediator = mediator;
                _cacheService = cacheService;
                _config = config;
                _logger = logger;
            }

            public async Task<Unit> Handle(RunDiplomaMigrationsCommand request, CancellationToken cancellationToken)
            {
                _logger.LogInformation("Bắt đầu chạy migration...");
                await _dataContext.Database.MigrateAsync();
                _logger.LogInformation("Migration hoàn tất!");

                //Xóa cache
                _cacheService.RemoveAll();
                return Unit.Value;
            }
        }
    }
}