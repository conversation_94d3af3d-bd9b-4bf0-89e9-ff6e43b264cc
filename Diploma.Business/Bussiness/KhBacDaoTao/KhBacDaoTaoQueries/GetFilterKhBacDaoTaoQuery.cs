using Core.Business;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Diploma.Data;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Diploma.Business
{
    public class GetFilterKhBacDaoTaoQuery : IRequest<PaginationList<KhBacDaoTaoBaseModel>>
    {
        public KhBacDaoTaoQueryFilter Filter { get; set; }

        /// <summary>
        /// L<PERSON>y danh sách bậc đào tạo khoa học theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterKhBacDaoTaoQuery(KhBacDaoTaoQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterKhBacDaoTaoQuery, PaginationList<KhBacDaoTaoBaseModel>>
        {
            private readonly DiplomaReadDataContext _dataContext;

            public Handler(DiplomaReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<KhBacDaoTaoBaseModel>> Handle(GetFilterKhBacDaoTaoQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.KhBacDaoTaos.AsNoTracking()
                            select dt);

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.Name.ToLower().Contains(ts) || x.Code.ToLower().Contains(ts));
                }

                if (filter.IsActive.HasValue)
                {
                    data = data.Where(x => x.IsActive == filter.IsActive);
                }

                data = data.OrderByField(filter.PropertyName, filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = 10; // Default page size
                }

                // Get total count asynchronously
                int totalCount = await data.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await data
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .ToListAsync();

                var listResult = AutoMapperUtils.AutoMap<KhBacDaoTao, KhBacDaoTaoBaseModel>(listData);

                return new PaginationList<KhBacDaoTaoBaseModel>()
                {
                    DataCount = listResult.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listResult
                };
            }
        }
    }
}
