using Core.Business;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Diploma.Data;
using Serilog;
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Diploma.Business
{
    public class CreateKhBacDaoTaoCommand : IRequest<Unit>
    {
        public CreateKhBacDaoTaoModel Model { get; set; }

        /// <summary>
        /// Thêm mới bậc đào tạo khoa học
        /// </summary>
        /// <param name="model">Thông tin bậc đào tạo khoa học cần thêm mới</param>
        public CreateKhBacDaoTaoCommand(CreateKhBacDaoTaoModel model)
        {
            Model = model;
        }

        public class Handler : IRequestHandler<CreateKhBacDaoTaoCommand, Unit>
        {
            private readonly DiplomaDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(DiplomaDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(CreateKhBacDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var model = request.Model;
                Log.Information($"Create {KhBacDaoTaoConstant.CachePrefix}: " + JsonSerializer.Serialize(model));

                var entity = AutoMapperUtils.AutoMap<CreateKhBacDaoTaoModel, KhBacDaoTao>(model);

                var checkCode = await _dataContext.KhBacDaoTaos.AnyAsync(x => x.Code == entity.Code);
                if (checkCode)
                {
                    throw new ArgumentException($"{_localizer["BacDaoTao.code.existed"]}");
                }

                await _dataContext.KhBacDaoTaos.AddAsync(entity);
                await _dataContext.SaveChangesAsync();

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Thêm mới bậc đào tạo khoa học mã: {entity.Code}",
                    ObjectCode = KhBacDaoTaoConstant.CachePrefix,
                    ObjectId = entity.Id.ToString()
                });

                //Xóa cache
                _cacheService.Remove(KhBacDaoTaoConstant.BuildCacheKey());

                return Unit.Value;
            }
        }
    }
}
