using Core.Business;
using Core.Data;
using Core.Shared;
using Core.Shared.ContextAccessor;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Diploma.Data;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Diploma.Business
{
    public class DeleteKhBacDaoTaoCommand : IRequest<Unit>
    {
        public int Id { get; set; }

        /// <summary>
        /// Xóa bậc đào tạo khoa học
        /// </summary>
        /// <param name="id">Id bậc đào tạo khoa học cần xóa</param>
        public DeleteKhBacDaoTaoCommand(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<DeleteKhBacDaoTaoCommand, Unit>
        {
            private readonly DiplomaDataContext _dataContext;
            private readonly ICacheService _cacheService;
            private readonly IStringLocalizer<Resources> _localizer;
            private readonly IContextAccessor _contextAccessor;

            public Handler(DiplomaDataContext dataContext, ICacheService cacheService, IStringLocalizer<Resources> localizer, Func<IContextAccessor> contextAccessorFactory)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
                _localizer = localizer;
                _contextAccessor = contextAccessorFactory();
            }

            public async Task<Unit> Handle(DeleteKhBacDaoTaoCommand request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                Log.Information($"Delete {KhBacDaoTaoConstant.CachePrefix}: {id}");

                var dt = await _dataContext.KhBacDaoTaos.FirstOrDefaultAsync(x => x.Id == id);

                if (dt == null)
                {
                    throw new ArgumentException($"{_localizer["data.not-found"]}");
                }
                _dataContext.KhBacDaoTaos.Remove(dt);

                _contextAccessor.SystemLog.ListAction.Add(new ActionDetail()
                {
                    Description = $"Xóa bậc đào tạo khoa học mã: {dt.Code}",
                    ObjectCode = KhBacDaoTaoConstant.CachePrefix
                });
                await _dataContext.SaveChangesAsync();

                //Xóa cache
                _cacheService.Remove(KhBacDaoTaoConstant.BuildCacheKey(id.ToString()));
                _cacheService.Remove(KhBacDaoTaoConstant.BuildCacheKey());

                Log.Information($"Delete {KhBacDaoTaoConstant.CachePrefix} completed");

                return Unit.Value;
            }
        }
    }
}
