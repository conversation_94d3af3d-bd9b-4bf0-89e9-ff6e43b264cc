image: thienanunisoft/net-sdk:9.0

services:
  - docker:dind

variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_TLS_CERTDIR: ""
  DOCKER_DRIVER: overlay2

  PROJECT_PATH: ./APIService/Diploma.API/Diploma.API.csproj
  IMAGE_NAME_TAG_TEST: thienanunisoft/diploma-api:test
  IMAGE_NAME_TAG_LATEST: thienanunisoft/diploma-api:latest
  DOCKER_FILE: Dockerfile/dockerfile_diploma_api_build
  MINIO_PATH: uni-diploma
  ZIP_FILE_NAME: uni-diploma-api.zip

stages:
  - build

# Build image and push to Docker Hub branch test
build_and_push_test:
  stage: build

  before_script:
    - git config --global url."https://$GITLAB_CI_USER:$<EMAIL>/".insteadOf "https://gitlab.unisoft.edu.vn/"
    - docker --version  # 🧪 Kiểm tra docker đã hoạt động
    - dotnet --version  # 🧪 Kiểm tra dotnet đã hoạt động
    - echo "Logging into Docker Hub..."
    - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
    - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
    - docker pull $IMAGE_NAME_TAG_TEST || true

  script:
    - git submodule update --init --recursive
    # Build ứng dụng .NET
    - dotnet restore $PROJECT_PATH
    - dotnet publish $PROJECT_PATH -c Release -o ./app-publish -f net8.0
    - echo "Build .NET project completed."

    # Xóa file cấu hình
    - rm -f ./app-publish/appsettings*.json

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_TEST . -f $DOCKER_FILE
    - docker push $IMAGE_NAME_TAG_TEST
    - echo "Docker image pushed to Docker Hub."

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: $CI_PROJECT_NAME-cache
    paths:
      - ~/.nuget/packages/

  only:
    - test

build_and_push_prod:
  stage: build

  before_script:
    - git config --global url."https://$GITLAB_CI_USER:$<EMAIL>/".insteadOf "https://gitlab.unisoft.edu.vn/"
    - docker --version  # 🧪 Kiểm tra docker đã hoạt động
    - dotnet --version  # 🧪 Kiểm tra dotnet đã hoạt động
    - echo "Logging into Docker Hub..."
    - echo "$DOCKERHUB_TOKEN" | docker login -u "$DOCKERHUB_USERNAME" --password-stdin
    - mc alias set $MINIO_ALIAS $MINIO_URL $MINIO_ACCESS_KEY $MINIO_SECRET_KEY
    - docker pull $IMAGE_NAME_TAG_LATEST || true

  script:
    - git submodule update --init --recursive
    # Build ứng dụng .NET
    - dotnet restore $PROJECT_PATH
    - dotnet publish $PROJECT_PATH -c Release -o app-publish -f net8.0
    - echo "Build .NET project completed."

    # Tạo file zip từ thư mục ./app-publish
    - zip -r $ZIP_FILE_NAME app-publish

    # Xóa file cấu hình
    - rm -f ./app-publish/appsettings*.json

    # Build Docker image và push lên Docker Hub
    - docker build -t $IMAGE_NAME_TAG_LATEST . -f $DOCKER_FILE
    - docker push $IMAGE_NAME_TAG_LATEST
    - echo "Docker image pushed to Docker Hub."

    # Copy file vào MinIO
    - echo "Copying $ZIP_FILE_NAME to MinIO..."
    - mc cp $ZIP_FILE_NAME $MINIO_ALIAS/$MINIO_BUCKET/$MINIO_PATH/

  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS="✅ CI build *SUCCESSED*"
        COLOR="#36a64f"
      else
        STATUS="❌ CI build *FAILED*"
        COLOR="#ff0000"
      fi

      MESSAGE="${STATUS}\n\
        📦 Project: *${CI_PROJECT_NAME}*\n\
        🌿 Branch: *${CI_COMMIT_REF_NAME}*\n\
        🔢 Commit: \`${CI_COMMIT_SHORT_SHA}\`\n\
        📝 Message: ${CI_COMMIT_MESSAGE}\n\
        👤 By: *${GITLAB_USER_NAME}*\n\
        🔗 <${CI_PIPELINE_URL}|Xem pipeline>"

      curl -X POST -H 'Content-type: application/json' --data "{
        \"attachments\": [
          {
            \"color\": \"${COLOR}\",
            \"mrkdwn_in\": [\"text\"],
            \"text\": \"${MESSAGE}\"
          }
        ]
      }" "$SLACK_WEBHOOK_URL"

  cache:
    key: $CI_PROJECT_NAME-cache
    paths:
      - ~/.nuget/packages/

  only:
    - master
