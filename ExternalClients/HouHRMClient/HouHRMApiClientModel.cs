using System.Text.Json.Serialization;
using System;
using System.Collections.Generic;

namespace HouHRMClient
{
    public class HouHRMApiClientConfiguration
    {
        public string? ApiUrl { get; set; }
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
    }

    public class HouResponseDataModel<T>
    {
        public bool IsSuccess { get; set; }
        public string? Message { get; set; }
        public T? Data { get; set; }
    }

    #region Auth
    public class HouAuthRequestModel
    {
        public string? Username { get; set; }
        public string? Password { get; set; }
    }
    public class HouAuthResponseModel
    {
        public string? Token { get; set; }
    }
    #endregion

    #region User Model
    public class HouUserRequestModel
    {
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
    }

    public class HouUserResponseModel
    {
        public int Id { get; set; }
        public string? UserName { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
    }
    #endregion

    #region Hou Model
    public class HouRequestModel
    {
        [JsonPropertyName("requestID")]
        public string RequestId { get; set; } = Guid.NewGuid().ToString();

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; }
    }

    public class HouResponseModel<T>
    {
        [JsonPropertyName("statusCode")]
        public string StatusCode { get; set; }
        public T Data { get; set; }
    }
    #endregion

    #region Đơn vị Model
    public class HouDonViResponseModel
    {
        [JsonPropertyName("code")]
        public string Code { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("ten_viet_tat")]
        public string TenVietTat { get; set; }

        [JsonPropertyName("ngay_thanh_lap")]
        public DateTime? NgayThanhLap { get; set; }

        [JsonPropertyName("dia_chi")]
        public string DiaChi { get; set; }

        [JsonPropertyName("so_dien_thoai")]
        public string SoDienThoai { get; set; }

        [JsonPropertyName("so_fax")]
        public string SoFax { get; set; }

        [JsonPropertyName("email")]
        public string Email { get; set; }

        [JsonPropertyName("ma_so_thue")]
        public string MaSoThue { get; set; }

        [JsonPropertyName("ghi_chu")]
        public string GhiChu { get; set; }
    }
    #endregion

    #region Phòng ban Model
    public class HouPhongBanResponseModel
    {
        [JsonPropertyName("code")]
        public string Code { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("ten_viet_tat")]
        public string TenVietTat { get; set; }

        [JsonPropertyName("dia_chi")]
        public string DiaChi { get; set; }

        [JsonPropertyName("so_dien_thoai")]
        public string SoDienThoai { get; set; }

        [JsonPropertyName("so_fax")]
        public string SoFax { get; set; }

        [JsonPropertyName("email")]
        public string Email { get; set; }

        [JsonPropertyName("ghi_chu")]
        public string GhiChu { get; set; }

        [JsonPropertyName("code_phong_ban_cha")]
        public string CodePhongBanCha { get; set; }

        [JsonPropertyName("code_don_vi")]
        public string CodeDonVi { get; set; }
    }
    #endregion

    #region Hồ sơ cán bộ
    public class HouHoSoCanBoResponseModel
    {
        [JsonPropertyName("ma_can_bo")]
        public string MaCanBo { get; set; }

        [JsonPropertyName("ho_ten")]
        public string HoTen { get; set; }

        [JsonPropertyName("url_anh")]
        public string UrlAnh { get; set; }

        [JsonPropertyName("ma_gioi_tinh")]
        public string MaGioiTinh { get; set; }

        [JsonPropertyName("ngay_sinh")]
        public DateTime? NgaySinh { get; set; }

        [JsonPropertyName("noi_sinh")]
        public string NoiSinh { get; set; }

        [JsonPropertyName("que_quan")]
        public string QueQuan { get; set; }

        [JsonPropertyName("ma_dan_toc")]
        public string MaDanToc { get; set; }

        [JsonPropertyName("dia_chi_lien_he")]
        public string DiaChiLienHe { get; set; }

        [JsonPropertyName("dien_thoai_ca_nhan")]
        public string DienThoaiCaNhan { get; set; }

        [JsonPropertyName("email")]
        public string Email { get; set; }

        [JsonPropertyName("cccd")]
        public string CCCD { get; set; }

        [JsonPropertyName("ngay_cap_cccd")]
        public DateTime? NgayCapCCCD { get; set; }

        [JsonPropertyName("noi_cap_cccd")]
        public string NoiCapCCCD { get; set; }

        [JsonPropertyName("ma_so_thue")]
        public string MaSoThue { get; set; }

        [JsonPropertyName("ma_hoc_vi")]
        public string MaHocVi { get; set; }

        [JsonPropertyName("nam_nhan_hoc_vi")]
        public int? NamNhanHocVi { get; set; }

        [JsonPropertyName("ma_chuc_danh")]
        public string MaChucDanh { get; set; }

        [JsonPropertyName("nam_nhan_chuc_danh")]
        public int? NamNhanChucDanh { get; set; }

        [JsonPropertyName("chuyen_nganh_nghien_cuu")]
        public string ChuyenNganhNghienCuu { get; set; }

        [JsonPropertyName("ma_chuc_vu")]
        public string MaChucVu { get; set; }

        [JsonPropertyName("dia_chi_co_quan")]
        public string DiaChiCoQuan { get; set; }

        [JsonPropertyName("dien_thoai_co_quan")]
        public string DienThoaiCoQuan { get; set; }

        [JsonPropertyName("email_co_quan")]
        public string EmailCoQuan { get; set; }

        [JsonPropertyName("fax_co_quan")]
        public string FaxCoQuan { get; set; }

        [JsonPropertyName("ma_phong_ban")]
        public string MaPhongBan { get; set; }

        [JsonPropertyName("tai_khoan_ngan_hang")]
        public string TaiKhoanNganHang { get; set; }

        [JsonPropertyName("ten_ngan_hang")]
        public string TenNganHang { get; set; }

        [JsonPropertyName("chi_nhanh_ngan_hang")]
        public string ChiNhanhNganHang { get; set; }

        [JsonPropertyName("qua_trinh_dao_tao")]
        public List<HouQuaTrinhDaoTaoResponseModel> QuaTrinhDaoTao { get; set; }

        [JsonPropertyName("ngoai_ngu")]
        public List<HouNgoaiNguResponseModel> NgoaiNgu { get; set; }

        [JsonPropertyName("dao_tao_ngan_han")]
        public List<HouDaoTaoNganHanResponseModel> DaoTaoNganHan { get; set; }

        [JsonPropertyName("qua_trinh_cong_tac")]
        public List<HouQuaTrinhCongTacResponseModel> QuaTrinhCongTac { get; set; }
    }

    public class HouQuaTrinhDaoTaoResponseModel
    {
        [JsonPropertyName("ma_bac_dao_tao")]
        public string MaBacDaoTao { get; set; }

        [JsonPropertyName("ma_chuyen_nganh")]
        public string MaChuyenNganh { get; set; }

        [JsonPropertyName("noi_dao_tao")]
        public string NoiDaoTao { get; set; }

        [JsonPropertyName("nam_tot_nghiep")]
        public int? NamTotNghiep { get; set; }

        [JsonPropertyName("ma_quoc_gia")]
        public string MaQuocGia { get; set; }

        [JsonPropertyName("ten_luan_an")]
        public string TenLuanAn { get; set; }

        [JsonPropertyName("ghi_chu")]
        public string GhiChu { get; set; }
    }

    public class HouNgoaiNguResponseModel
    {
        [JsonPropertyName("ten_ngoai_ngu")]
        public string TenNgoaiNgu { get; set; }

        [JsonPropertyName("trinh_do")]
        public string TrinhDo { get; set; }

        [JsonPropertyName("diem_nghe")]
        public double? DiemNghe { get; set; }

        [JsonPropertyName("diem_noi")]
        public double? DiemNoi { get; set; }

        [JsonPropertyName("diem_doc")]
        public double? DiemDoc { get; set; }

        [JsonPropertyName("diem_viet")]
        public double? DiemViet { get; set; }

        [JsonPropertyName("ghi_chu")]
        public string GhiChu { get; set; }
    }

    public class HouDaoTaoNganHanResponseModel
    {
        [JsonPropertyName("ten_khoa_hoc")]
        public string TenKhoaHoc { get; set; }

        [JsonPropertyName("noi_dung_dao_tao")]
        public string NoiDungDaoTao { get; set; }

        [JsonPropertyName("don_vi_dao_tao")]
        public string DonViDaoTao { get; set; }

        [JsonPropertyName("chung_chi")]
        public string ChungChi { get; set; }

        [JsonPropertyName("tu_ngay")]
        public DateTime? TuNgay { get; set; }

        [JsonPropertyName("den_ngay")]
        public DateTime? DenNgay { get; set; }

        [JsonPropertyName("ghi_chu")]
        public string GhiChu { get; set; }
    }

    public class HouQuaTrinhCongTacResponseModel
    {
        [JsonPropertyName("tu_nam")]
        public int? TuNam { get; set; }

        [JsonPropertyName("den_nam")]
        public int? DenNam { get; set; }

        [JsonPropertyName("noi_cong_tac")]
        public string NoiCongTac { get; set; }

        [JsonPropertyName("chuc_vu")]
        public string ChucVu { get; set; }

        [JsonPropertyName("cong_viec_dam_nhan")]
        public string CongViecDamNhan { get; set; }

        [JsonPropertyName("ghi_chu")]
        public string GhiChu { get; set; }
    }
    #endregion

    #region Danh mục Dân tộc
    public class HouDanTocResponseModel
    {
        [JsonPropertyName("ma_dan_toc")]
        public string Code { get; set; }

        [JsonPropertyName("ten_dan_toc")]
        public string Name { get; set; }
    }
    #endregion

    #region Danh mục Học vị
    public class HouHocViResponseModel
    {
        [JsonPropertyName("ma_hoc_vi")]
        public string Code { get; set; }

        [JsonPropertyName("ten_hoc_vi")]
        public string Name { get; set; }
    }
    #endregion

    #region Danh mục Chức danh
    public class HouChucDanhResponseModel
    {
        [JsonPropertyName("ma_chuc_danh")]
        public string Code { get; set; }

        [JsonPropertyName("ten_chuc_danh")]
        public string Name { get; set; }
    }
    #endregion

    #region Danh mục Chức vụ
    public class HouChucVuResponseModel
    {
        [JsonPropertyName("ma_chuc_vu")]
        public string Code { get; set; }

        [JsonPropertyName("ten_chuc_vu")]
        public string Name { get; set; }
    }
    #endregion

    #region Danh mục Bậc đào tạo
    public class HouBacDaoTaoResponseModel
    {
        [JsonPropertyName("ma_bac_dao_tao")]
        public string Code { get; set; }

        [JsonPropertyName("ten_bac_dao_tao")]
        public string Name { get; set; }
    }
    #endregion

    #region Danh mục Quốc gia
    public class HouQuocGiaResponseModel
    {
        [JsonPropertyName("ma_quoc_gia")]
        public string Code { get; set; }

        [JsonPropertyName("ten_quoc_gia")]
        public string Name { get; set; }
    }
    #endregion

    #region Danh mục Giới tính
    public class HouGioiTinhResponseModel
    {
        [JsonPropertyName("ma_gioi_tinh")]
        public string Code { get; set; }

        [JsonPropertyName("ten_gioi_tinh")]
        public string Name { get; set; }
    }
    #endregion
}