using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace HouHRMClient
{
    public class HouHRMApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly HouHRMApiClientConfiguration _config;

        public HouHRMApiClient(HouHRMApiClientConfiguration config, HttpClient httpClient)
        {
            _config = config;
            _httpClient = httpClient;
        }

        // Hàm login trả về token
        public async Task<string> LoginAsync(string username, string password)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/api/login");
            request.Content = new FormUrlEncodedContent(new Dictionary<string, string>
            {
                { "userName", _config.ClientId },
                { "password", _config.ClientSecret }
            });
            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<HouAuthResponseModel>>(content);
                return rs?.Data?.Token ?? throw new Exception("User data is null");
            }
            else
            {
                throw new Exception("Login failed");
            }
        }

        // Lấy thông tin người dùng theo email
        public async Task<HouUserResponseModel> GetUserByEmailAsync(string email)
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_config.ApiUrl}/api/users?email={email}");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));
            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(); // Trả về thông tin người dùng
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<HouUserResponseModel>>(content);
                return rs?.Data ?? throw new Exception("User data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách người dùng
        public async Task<List<HouUserResponseModel>> GetAllUsersAsync()
        {
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_config.ApiUrl}/api/users/all");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));
            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(); // Trả về danh sách người dùng
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouUserResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("User data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Đơn vị
        public async Task<List<HouDonViResponseModel>> GetAllDonViAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/to-chuc/don-vi");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouDonViResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Phòng ban
        public async Task<List<HouPhongBanResponseModel>> GetAllPhongBanAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/to-chuc/phong-ban");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouPhongBanResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Hồ sơ cán bộ
        public async Task<List<HouHoSoCanBoResponseModel>> GetAllHoSoCanBoAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/ho-so-can-bo");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouHoSoCanBoResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Dân tộc
        public async Task<List<HouDanTocResponseModel>> GetAllDanTocAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/danh-muc/dan-toc");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouDanTocResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Học vị
        public async Task<List<HouHocViResponseModel>> GetAllHocViAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/danh-muc/hoc-vi");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouHocViResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Chức danh
        public async Task<List<HouChucDanhResponseModel>> GetAllChucDanhAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/danh-muc/chuc-danh");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouChucDanhResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Chức vụ
        public async Task<List<HouChucVuResponseModel>> GetAllChucVuAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/danh-muc/chuc-vu");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouChucVuResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Bậc đào tạo
        public async Task<List<HouBacDaoTaoResponseModel>> GetAllBacDaoTaoAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/danh-muc/bac-dao-tao");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouBacDaoTaoResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Quốc gia
        public async Task<List<HouQuocGiaResponseModel>> GetAllQuocGiaAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/danh-muc/quoc-gia");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouQuocGiaResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }

        // Lấy danh sách Giới tính
        public async Task<List<HouGioiTinhResponseModel>> GetAllGioiTinhAsync(HouRequestModel houRequest)
        {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_config.ApiUrl}/sync-data/danh-muc/gioi-tinh");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", await LoginAsync(_config.ClientId, _config.ClientSecret));

            var json = System.Text.Json.JsonSerializer.Serialize(houRequest);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var rs = System.Text.Json.JsonSerializer.Deserialize<HouResponseDataModel<List<HouGioiTinhResponseModel>>>(content);
                return rs?.Data ?? throw new Exception("Data is null");
            }
            else
            {
                throw new Exception("Failed to retrieve user information");
            }
        }
    }
}
