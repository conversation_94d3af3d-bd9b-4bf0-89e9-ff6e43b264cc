using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Net.Http;

namespace HouHRMClient
{
    public static class ServiceConfigure
    {
        public static IServiceCollection Configure(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<HouHRMApiClient>(provider =>
            {
                // var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
                var httpClient = provider.GetRequiredService<HttpClient>();

                var config = configuration.GetSection("HRMClient:Hou").Get<HouHRMApiClientConfiguration>();

                return new HouHRMApiClient(config, httpClient);
            });
            return services;
        }
    }
}
