using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared
{
    public class PermissionSeedModel
    {
        public PermissionSeedModel()
        {
            IsActive = true;
        }
        public PermissionSeedModel(string groupName, string code, string name)
        {
            GroupName = groupName;
            Code = code;
            Name = name;
            IsActive = true;
        }
        public string GroupName { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public int Order { get; set; }
    }

    public enum PermissionSystemEnum
    {
        [Display(GroupName = "Quản lý quy trình", Name = "Thêm mới quy trình")]
        WORKFLOW_ADD,

        [Display(GroupName = "Quản lý quy trình", Name = "Cập nhật quy trình")]
        WORKFLOW_EDIT,

        //[Display(GroupName = "Quản lý quy trình", Name = "Xóa quy trình")]
        //WORKFLOW_DELETE,

        [Display(GroupName = "Quản lý quy trình", Name = "Xem thông tin quy trình")]
        WORKFLOW_VIEW,

        [Display(GroupName = "Nhật ký hệ thống", Name = "Xem thông tin nhật ký hệ thống")]
        NHAT_KY_HE_THONG_VIEW,

        [Display(GroupName = "Nhật ký quên mật khẩu", Name = "Xem thông tin nhật ký quên mật khẩu")]
        FORGOT_PASSWORD_LOG_VIEW,

        [Display(GroupName = "Nhật ký gửi mail", Name = "Xem thông tin nhật ký gửi mail")]
        SEND_MAIL_LOG_VIEW,

        [Display(GroupName = "Nhật ký gửi mail", Name = "Gửi lại email")]
        SEND_MAIL_LOG_RESEND,

        [Display(GroupName = "Quản lý người dùng", Name = "Thêm mới người dùng")]
        QUAN_LY_NGUOI_DUNG_ADD,

        [Display(GroupName = "Quản lý người dùng", Name = "Cập nhật người dùng")]
        QUAN_LY_NGUOI_DUNG_EDIT,

        [Display(GroupName = "Quản lý người dùng", Name = "Xóa người dùng")]
        QUAN_LY_NGUOI_DUNG_DELETE,

        [Display(GroupName = "Quản lý người dùng", Name = "Gửi mail quên mật khẩu")]
        QUAN_LY_NGUOI_DUNG_SEND_MAIL_FORGOT_PASSWORD,

        [Display(GroupName = "Quản lý người dùng", Name = "Xem thông tin quản lý người dùng")]
        QUAN_LY_NGUOI_DUNG_VIEW,

        [Display(GroupName = "Quản lý người dùng", Name = "Phân quyền người dùng")]
        QUAN_LY_NGUOI_DUNG_UPDATE_ROLE,

        [Display(GroupName = "Quản lý người dùng", Name = "Gán lớp quản lý")]
        QUAN_LY_NGUOI_DUNG_ASSIGN_CLASS,

        [Display(GroupName = "Quản lý người dùng", Name = "Xóa quyền quản lý lớp")]
        QUAN_LY_NGUOI_DUNG_DELETE_ASSIGN_CLASS,

        [Display(GroupName = "Nhóm người dùng", Name = "Xem thông tin nhóm người dùng")]
        NHOM_NGUOI_DUNG_VIEW,

        [Display(GroupName = "Nhóm người dùng", Name = "Thêm mới nhóm người dùng")]
        NHOM_NGUOI_DUNG_ADD,

        [Display(GroupName = "Nhóm người dùng", Name = "Cập nhật nhóm người dùng")]
        NHOM_NGUOI_DUNG_EDIT,

        [Display(GroupName = "Nhóm người dùng", Name = "Xóa nhóm người dùng")]
        NHOM_NGUOI_DUNG_DELETE,

        [Display(GroupName = "Nhóm người dùng", Name = "Phân quyền theo người dùng")]
        NHOM_NGUOI_DUNG_UPDATE_USER,

        [Display(GroupName = "Nhóm người dùng", Name = "Phân quyền chức năng")]
        NHOM_NGUOI_DUNG_UPDATE_PERMISSION,

        [Display(GroupName = "Mẫu email", Name = "Xem thông tin mẫu email")]
        EMAIL_TEMPLATE_VIEW,

        [Display(GroupName = "Mẫu email", Name = "Thêm mới mẫu email")]
        EMAIL_TEMPLATE_ADD,

        [Display(GroupName = "Mẫu email", Name = "Cập nhật mẫu email")]
        EMAIL_TEMPLATE_EDIT,

        //[Display(GroupName = "Mẫu email", Name = "Xóa mẫu email")]
        //EMAIL_TEMPLATE_DELETE

        [Display(GroupName = "Chứng thư số", Name = "Xem thông tin chứng thư số")]
        CHUNG_THU_SO_VIEW,

        [Display(GroupName = "Chứng thư số", Name = "Thêm mới chứng thư số")]
        CHUNG_THU_SO_ADD,

        [Display(GroupName = "Chứng thư số", Name = "Cập nhật chứng thư số")]
        CHUNG_THU_SO_EDIT,

        [Display(GroupName = "Chứng thư số", Name = "Xóa chứng thư số")]
        CHUNG_THU_SO_DELETE,

        [Display(GroupName = "Mẫu chữ ký", Name = "Xem thông tin mẫu chữ ký")]
        MAU_CHU_KY_VIEW,

        [Display(GroupName = "Mẫu chữ ký", Name = "Thêm mới mẫu chữ ký")]
        MAU_CHU_KY_ADD,

        [Display(GroupName = "Mẫu chữ ký", Name = "Cập nhật mẫu chữ ký")]
        MAU_CHU_KY_EDIT,

        [Display(GroupName = "Mẫu chữ ký", Name = "Xóa mẫu chữ ký")]
        MAU_CHU_KY_DELETE,

        [Display(GroupName = "Visnam tài khoản kết nối", Name = "Xem thông tin tài khoản kết nối Visnam")]
        VISNAM_TAI_KHOAN_KET_NOI_VIEW,

        [Display(GroupName = "Visnam tài khoản kết nối", Name = "Thêm mới tài khoản kết nối Visnam")]
        VISNAM_TAI_KHOAN_KET_NOI_ADD,

        [Display(GroupName = "Visnam tài khoản kết nối", Name = "Cập nhật tài khoản kết nối Visnam")]
        VISNAM_TAI_KHOAN_KET_NOI_EDIT,

        [Display(GroupName = "Visnam tài khoản kết nối", Name = "Xóa tài khoản kết nối Visnam")]
        VISNAM_TAI_KHOAN_KET_NOI_DELETE,

        [Display(GroupName = "Hệ thống", Name = "Xóa cache hệ thống")]
        CLEAR_ALL_CACHE,


        [Display(GroupName = "Hệ đào tạo", Name = "Xem thông tin hệ đào tạo")]
        HE_VIEW,

        [Display(GroupName = "Hệ đào tạo", Name = "Thêm thông tin hệ đào tạo")]
        HE_ADD,

        [Display(GroupName = "Hệ đào tạo", Name = "Sửa thông tin hệ đào tạo")]
        HE_EDIT,

        [Display(GroupName = "Hệ đào tạo", Name = "Xóa thông tin hệ đào tạo")]
        HE_DELETE,


        [Display(GroupName = "Tôn giáo", Name = "Xem thông tin tôn giáo")]
        TON_GIAO_VIEW,

        [Display(GroupName = "Tôn giáo", Name = "Thêm thông tin tôn giáo")]
        TON_GIAO_ADD,

        [Display(GroupName = "Tôn giáo", Name = "Sửa thông tin tôn giáo")]
        TON_GIAO_EDIT,

        [Display(GroupName = "Tôn giáo", Name = "Xóa thông tin tôn giáo")]
        TON_GIAO_DELETE,

        [Display(GroupName = "Điểm công thức", Name = "Xem thông tin điểm công thức")]
        DIEM_CONG_THUC_VIEW,

        [Display(GroupName = "Điểm công thức", Name = "Thêm thông tin điểm công thức")]
        DIEM_CONG_THUC_ADD,

        [Display(GroupName = "Điểm công thức", Name = "Sửa thông tin điểm công thức")]
        DIEM_CONG_THUC_EDIT,

        [Display(GroupName = "Điểm công thức", Name = "Xóa thông tin điểm công thức")]
        DIEM_CONG_THUC_DELETE,

        [Display(GroupName = "Quốc tịch", Name = "Xem thông tin quốc tịch")]
        QUOC_TICH_VIEW,

        [Display(GroupName = "Quốc tịch", Name = "Thêm thông tin quốc tịch")]
        QUOC_TICH_ADD,

        [Display(GroupName = "Quốc tịch", Name = "Sửa thông tin quốc tịch")]
        QUOC_TICH_EDIT,

        [Display(GroupName = "Quốc tịch", Name = "Xóa thông tin quốc tịch")]
        QUOC_TICH_DELETE,


        [Display(GroupName = "Chức danh", Name = "Xem thông tin chức danh")]
        CHUC_DANH_VIEW,

        [Display(GroupName = "Chức danh", Name = "Thêm thông tin chức danh")]
        CHUC_DANH_ADD,

        [Display(GroupName = "Chức danh", Name = "Sửa thông tin chức danh")]
        CHUC_DANH_EDIT,

        [Display(GroupName = "Chức danh", Name = "Xóa thông tin chức danh")]
        CHUC_DANH_DELETE,


        [Display(GroupName = "Giới tính", Name = "Xem thông tin giới tính")]
        GIOI_TINH_VIEW,

        [Display(GroupName = "Giới tính", Name = "Thêm thông tin giới tính")]
        GIOI_TINH_ADD,

        [Display(GroupName = "Giới tính", Name = "Sửa thông tin giới tính")]
        GIOI_TINH_EDIT,

        [Display(GroupName = "Giới tính", Name = "Xóa thông tin giới tính")]
        GIOI_TINH_DELETE,

        [Display(GroupName = "Lớp", Name = "Xem thông tin lớp")]
       LOP_VIEW,

        [Display(GroupName = "Lớp", Name = "Thêm thông tin lớp")]
       LOP_ADD,

        [Display(GroupName = "Lớp", Name = "Sửa thông tin lớp")]
       LOP_EDIT,

        [Display(GroupName = "Lớp", Name = "Xóa thông tin lớp")]
       LOP_DELETE,


        [Display(GroupName = "Học vị", Name = "Xem thông tin học vị")]
        HOC_VI_VIEW,

        [Display(GroupName = "Học vị", Name = "Thêm thông tin học vị")]
        HOC_VI_ADD,

        [Display(GroupName = "Học vị", Name = "Sửa thông tin học vị")]
        HOC_VI_EDIT,

        [Display(GroupName = "Học vị", Name = "Xóa thông tin học vị")]
        HOC_VI_DELETE,


        [Display(GroupName = "Học hàm", Name = "Xem thông tin học hàm")]
        HOC_HAM_VIEW,

        [Display(GroupName = "Học hàm", Name = "Thêm thông tin học hàm")]
        HOC_HAM_ADD,

        [Display(GroupName = "Học hàm", Name = "Sửa thông tin học hàm")]
        HOC_HAM_EDIT,

        [Display(GroupName = "Học hàm", Name = "Xóa thông tin học hàm")]
        HOC_HAM_DELETE,


        [Display(GroupName = "Dân tộc", Name = "Xem thông tin dân tộc")]
        DAN_TOC_VIEW,

        [Display(GroupName = "Dân tộc", Name = "Thêm thông tin dân tộc")]
        DAN_TOC_ADD,

        [Display(GroupName = "Dân tộc", Name = "Sửa thông tin dân tộc")]
        DAN_TOC_EDIT,

        [Display(GroupName = "Dân tộc", Name = "Xóa thông tin dân tộc")]
        DAN_TOC_DELETE,



        [Display(GroupName = "Khoa", Name = "Xem thông tin khoa")]
        KHOA_VIEW,

        [Display(GroupName = "Khoa", Name = "Thêm thông tin khoa")]
        KHOA_ADD,

        [Display(GroupName = "Khoa", Name = "Sửa thông tin khoa")]
        KHOA_EDIT,

        [Display(GroupName = "Khoa", Name = "Xóa thông tin khoa")]
        KHOA_DELETE,


        [Display(GroupName = "Ngành", Name = "Xem thông tin ngành")]
        NGANH_VIEW,

        [Display(GroupName = "Ngành", Name = "Thêm thông tin ngành")]
        NGANH_ADD,

        [Display(GroupName = "Ngành", Name = "Sửa thông tin ngành")]
        NGANH_EDIT,

        [Display(GroupName = "Ngành", Name = "Xóa thông tin ngành")]
        NGANH_DELETE,


        [Display(GroupName = "Huyện", Name = "Xem thông tin huyện")]
        HUYEN_VIEW,

        [Display(GroupName = "Huyện", Name = "Thêm thông tin huyện")]
        HUYEN_ADD,

        [Display(GroupName = "Huyện", Name = "Sửa thông tin huyện")]
        HUYEN_EDIT,

        [Display(GroupName = "Huyện", Name = "Xóa thông tin huyện")]
        HUYEN_DELETE,


        [Display(GroupName = "Chuyên ngành", Name = "Xem thông tin chuyên ngành")]
        CHUYEN_NGANH_VIEW,

        [Display(GroupName = "Chuyên ngành", Name = "Thêm thông tin chuyên ngành")]
        CHUYEN_NGANH_ADD,

        [Display(GroupName = "Chuyên ngành", Name = "Sửa thông tin chuyên ngành")]
        CHUYEN_NGANH_EDIT,

        [Display(GroupName = "Chuyên ngành", Name = "Xóa thông tin chuyên ngành")]
        CHUYEN_NGANH_DELETE,



        [Display(GroupName = "Tỉnh", Name = "Xem thông tin tỉnh")]
        TINH_VIEW,

        [Display(GroupName = "Tỉnh", Name = "Thêm thông tin tỉnh")]
        TINH_ADD,

        [Display(GroupName = "Tỉnh", Name = "Sửa thông tin tỉnh")]
        TINH_EDIT,

        [Display(GroupName = "Tỉnh", Name = "Xóa thông tin tỉnh")]
        TINH_DELETE,


        [Display(GroupName = "Khu vực", Name = "Xem thông tin khu vực")]
        KHU_VUC_VIEW,

        [Display(GroupName = "Khu vực", Name = "Thêm thông tin khu vực")]
        KHU_VUC_ADD,

        [Display(GroupName = "Khu vực", Name = "Sửa thông tin khu vực")]
        KHU_VUC_EDIT,

        [Display(GroupName = "Khu vực", Name = "Xóa thông tin khu vực")]
        KHU_VUC_DELETE,


        [Display(GroupName = "Nhóm đối tượng", Name = "Xem thông tin nhóm đối tượng")]
        NHOM_DOI_TUONG_VIEW,

        [Display(GroupName = "Nhóm đối tượng", Name = "Thêm thông tin nhóm đối tượng")]
        NHOM_DOI_TUONG_ADD,

        [Display(GroupName = "Nhóm đối tượng", Name = "Sửa thông tin nhóm đối tượng")]
        NHOM_DOI_TUONG_EDIT,

        [Display(GroupName = "Nhóm đối tượng", Name = "Xóa thông tin nhóm đối tượng")]
        NHOM_DOI_TUONG_DELETE,


        [Display(GroupName = "Đối tượng", Name = "Xem thông tin đối tượng")]
        DOI_TUONG_VIEW,

        [Display(GroupName = "Đối tượng", Name = "Thêm thông tin đối tượng")]
        DOI_TUONG_ADD,

        [Display(GroupName = "Đối tượng", Name = "Sửa thông tin đối tượng")]
        DOI_TUONG_EDIT,

        [Display(GroupName = "Đối tượng", Name = "Xóa thông tin đối tượng")]
        DOI_TUONG_DELETE,


        [Display(GroupName = "Đối tượng học bổng", Name = "Xem thông tin đối tượng học bổng")]
        DOI_TUONG_HOC_BONG_VIEW,

        [Display(GroupName = "Đối tượng học bổng", Name = "Thêm thông tin đối tượng học bổng")]
        DOI_TUONG_HOC_BONG_ADD,

        [Display(GroupName = "Đối tượng học bổng", Name = "Sửa thông tin đối tượng học bổng")]
        DOI_TUONG_HOC_BONG_EDIT,

        [Display(GroupName = "Đối tượng học bổng", Name = "Xóa thông tin đối tượng học bổng")]
        DOI_TUONG_HOC_BONG_DELETE,


        [Display(GroupName = "Cấp khen thưởng kỷ luật", Name = "Xem thông tin cấp khen thưởng kỷ luật")]
        CAP_KHEN_THUONG_KY_LUAT_VIEW,

        [Display(GroupName = "Cấp khen thưởng kỷ luật", Name = "Thêm thông tin cấp khen thưởng kỷ luật")]
        CAP_KHEN_THUONG_KY_LUAT_ADD,

        [Display(GroupName = "Cấp khen thưởng kỷ luật", Name = "Sửa thông tin cấp khen thưởng kỷ luật")]
        CAP_KHEN_THUONG_KY_LUAT_EDIT,

        [Display(GroupName = "Cấp khen thưởng kỷ luật", Name = "Xóa thông tin cấp khen thưởng kỷ luật")]
        CAP_KHEN_THUONG_KY_LUAT_DELETE,


        [Display(GroupName = "Loại khen thưởng", Name = "Xem thông tin loại khen thưởng")]
        LOAI_KHEN_THUONG_VIEW,

        [Display(GroupName = "Loại khen thưởng", Name = "Thêm thông tin loại khen thưởng")]
        LOAI_KHEN_THUONG_ADD,

        [Display(GroupName = "Loại khen thưởng", Name = "Sửa thông tin loại khen thưởng")]
        LOAI_KHEN_THUONG_EDIT,

        [Display(GroupName = "Loại khen thưởng", Name = "Xóa thông tin loại khen thưởng")]
        LOAI_KHEN_THUONG_DELETE,


        [Display(GroupName = "Hành vi kỷ luật", Name = "Xem thông tin hành vi kỷ luật")]
        HANH_VI_VIEW,

        [Display(GroupName = "Hành vi kỷ luật", Name = "Thêm thông tin hành vi kỷ luật")]
        HANH_VI_ADD,

        [Display(GroupName = "Hành vi kỷ luật", Name = "Sửa thông tin hành vi kỷ luật")]
        HANH_VI_EDIT,

        [Display(GroupName = "Hành vi kỷ luật", Name = "Xóa thông tin hành vi kỷ luật")]
        HANH_VI_DELETE,


        [Display(GroupName = "Xử lý kỷ luật", Name = "Xem thông tin xử lý kỷ luật")]
        XU_LY_VIEW,

        [Display(GroupName = "Xử lý kỷ luật", Name = "Thêm thông tin xử lý kỷ luật")]
        XU_LY_ADD,

        [Display(GroupName = "Xử lý kỷ luật", Name = "Sửa thông tin xử lý kỷ luật")]
        XU_LY_EDIT,

        [Display(GroupName = "Xử lý kỷ luật", Name = "Xóa thông tin xử lý kỷ luật")]
        XU_LY_DELETE,


        [Display(GroupName = "Loại rèn luyện", Name = "Xem thông tin loại rèn luyện")]
        LOAI_REN_LUYEN_VIEW,

        [Display(GroupName = "Loại rèn luyện", Name = "Thêm thông tin loại rèn luyện")]
        LOAI_REN_LUYEN_ADD,

        [Display(GroupName = "Loại rèn luyện", Name = "Sửa thông tin loại rèn luyện")]
        LOAI_REN_LUYEN_EDIT,

        [Display(GroupName = "Loại rèn luyện", Name = "Xóa thông tin loại rèn luyện")]
        LOAI_REN_LUYEN_DELETE,



        [Display(GroupName = "Phòng học", Name = "Xem thông tin phòng học")]
        PHONG_HOC_VIEW,

        [Display(GroupName = "Phòng học", Name = "Thêm thông tin phòng học")]
        PHONG_HOC_ADD,

        [Display(GroupName = "Phòng học", Name = "Sửa thông tin phòng học")]
        PHONG_HOC_EDIT,

        [Display(GroupName = "Phòng học", Name = "Xóa thông tin phòng học")]
        PHONG_HOC_DELETE,


        [Display(GroupName = "Hình thức học", Name = "Xem thông tin hình thức học")]
        HINH_THUC_HOC_VIEW,

        [Display(GroupName = "Hình thức học", Name = "Thêm thông tin hình thức học")]
        HINH_THUC_HOC_ADD,

        [Display(GroupName = "Hình thức học", Name = "Sửa thông tin hình thức học")]
        HINH_THUC_HOC_EDIT,

        [Display(GroupName = "Hình thức học", Name = "Xóa thông tin hình thức học")]
        HINH_THUC_HOC_DELETE,


        [Display(GroupName = "Chức vụ", Name = "Xem thông tin chức vụ")]
        CHUC_VU_VIEW,

        [Display(GroupName = "Chức vụ", Name = "Thêm thông tin chức vụ")]
        CHUC_VU_ADD,

        [Display(GroupName = "Chức vụ", Name = "Sửa thông tin chức vụ")]
        CHUC_VU_EDIT,

        [Display(GroupName = "Chức vụ", Name = "Xóa thông tin chức vụ")]
        CHUC_VU_DELETE,


        [Display(GroupName = "Học kỳ đăng ký", Name = "Xem thông tin học kỳ đăng ký")]
        HOC_KY_DANG_KY_VIEW,

        [Display(GroupName = "Học kỳ đăng ký", Name = "Thêm thông tin học kỳ đăng ký")]
        HOC_KY_DANG_KY_ADD,

        [Display(GroupName = "Học kỳ đăng ký", Name = "Sửa thông tin học kỳ đăng ký")]
        HOC_KY_DANG_KY_EDIT,

        [Display(GroupName = "Học kỳ đăng ký", Name = "Xóa thông tin học kỳ đăng ký")]
        HOC_KY_DANG_KY_DELETE,


        [Display(GroupName = "Xếp loại rèn luyện", Name = "Xem thông tin xếp loại rèn luyện")]
        XEP_LOAI_REN_LUYEN_VIEW,

        [Display(GroupName = "Xếp loại rèn luyện", Name = "Thêm thông tin xếp loại rèn luyện")]
        XEP_LOAI_REN_LUYEN_ADD,

        [Display(GroupName = "Xếp loại rèn luyện", Name = "Sửa thông tin xếp loại rèn luyện")]
        XEP_LOAI_REN_LUYEN_EDIT,

        [Display(GroupName = "Xếp loại rèn luyện", Name = "Xóa thông tin xếp loại rèn luyện")]
        XEP_LOAI_REN_LUYEN_DELETE,


        [Display(GroupName = "Loại giấy tờ", Name = "Xem thông tin loại giấy tờ")]
        LOAI_GIAY_TO_VIEW,

        [Display(GroupName = "Loại giấy tờ", Name = "Thêm thông tin loại giấy tờ")]
        LOAI_GIAY_TO_ADD,

        [Display(GroupName = "Loại giấy tờ", Name = "Sửa thông tin loại giấy tờ")]
        LOAI_GIAY_TO_EDIT,

        [Display(GroupName = "Loại giấy tờ", Name = "Xóa thông tin loại giấy tờ")]
        LOAI_GIAY_TO_DELETE,


        [Display(GroupName = "Xếp loại học bổng", Name = "Xem thông tin xếp loại học bổng")]
        XEP_LOAI_HOC_BONG_VIEW,

        [Display(GroupName = "Xếp loại học bổng", Name = "Thêm thông tin xếp loại học bổng")]
        XEP_LOAI_HOC_BONG_ADD,

        [Display(GroupName = "Xếp loại học bổng", Name = "Sửa thông tin xếp loại học bổng")]
        XEP_LOAI_HOC_BONG_EDIT,

        [Display(GroupName = "Xếp loại học bổng", Name = "Xóa thông tin xếp loại học bổng")]
        XEP_LOAI_HOC_BONG_DELETE,


        [Display(GroupName = "Phường", Name = "Xem thông tin phường")]
        PHUONG_VIEW,

        [Display(GroupName = "Phường", Name = "Thêm thông tin phường")]
        PHUONG_ADD,

        [Display(GroupName = "Phường", Name = "Sửa thông tin phường")]
        PHUONG_EDIT,

        [Display(GroupName = "Phường", Name = "Xóa thông tin phường")]
        PHUONG_DELETE,

        [Display(GroupName = "Xã", Name = "Xem thông tin xã")]
        XA_VIEW,

        [Display(GroupName = "Xã", Name = "Thêm thông tin xã")]
        XA_ADD,

        [Display(GroupName = "Xã", Name = "Sửa thông tin xã")]
        XA_EDIT,

        [Display(GroupName = "Xã", Name = "Xóa thông tin xã")]
        XA_DELETE,

        [Display(GroupName = "Loại điểm thành phần", Name = "Xem thông tin Loại điểm thành phần")]
        LOAI_DIEM_THANH_PHAN_VIEW,

        [Display(GroupName = "Loại điểm thành phần", Name = "Thêm thông tin Loại điểm thành phần")]
        LOAI_DIEM_THANH_PHAN_ADD,

        [Display(GroupName = "Loại điểm thành phần", Name = "Sửa thông tin Loại điểm thành phần")]
        LOAI_DIEM_THANH_PHAN_EDIT,

        [Display(GroupName = "Loại điểm thành phần", Name = "Xóa thông tin Loại điểm thành phần")]
        LOAI_DIEM_THANH_PHAN_DELETE,

        [Display(GroupName = "Xếp loại học tập thang điểm 10", Name = "Xem thông tin Xếp loại học tập thang điểm 10")]
        XEP_LOAI_HOC_TAP_THANG_DIEM_10_VIEW,

        [Display(GroupName = "Xếp loại học tập thang điểm 10", Name = "Thêm thông tin Xếp loại học tập thang điểm 10")]
        XEP_LOAI_HOC_TAP_THANG_DIEM_10_ADD,

        [Display(GroupName = "Xếp loại học tập thang điểm 10", Name = "Sửa thông tin Xếp loại học tập thang điểm 10")]
        XEP_LOAI_HOC_TAP_THANG_DIEM_10_EDIT,

        [Display(GroupName = "Xếp loại học tập thang điểm 10", Name = "Xóa thông tin Xếp loại học tập thang điểm 10")]
        XEP_LOAI_HOC_TAP_THANG_DIEM_10_DELETE,

        [Display(GroupName = "Xếp hạng năm đào tạo", Name = "Xem thông tin Xếp hạng năm đào tạo")]
        XEP_HANG_NAM_DAO_TAO_VIEW,

        [Display(GroupName = "Xếp hạng năm đào tạo", Name = "Thêm thông tin Xếp hạng năm đào tạo")]
        XEP_HANG_NAM_DAO_TAO_ADD,

        [Display(GroupName = "Xếp hạng năm đào tạo", Name = "Sửa thông tin Xếp hạng năm đào tạo")]
        XEP_HANG_NAM_DAO_TAO_EDIT,

        [Display(GroupName = "Xếp hạng năm đào tạo", Name = "Xóa thông tin Xếp hạng năm đào tạo")]
        XEP_HANG_NAM_DAO_TAO_DELETE,

        [Display(GroupName = "Xếp hạng học lực", Name = "Xem thông tin Xếp hạng học lực")]
        XEP_HANG_HOC_LUC_VIEW,

        [Display(GroupName = "Xếp hạng học lực", Name = "Thêm thông tin Xếp hạng học lực")]
        XEP_HANG_HOC_LUC_ADD,

        [Display(GroupName = "Xếp hạng học lực", Name = "Sửa thông tin Xếp hạng học lực")]
        XEP_HANG_HOC_LUC_EDIT,

        [Display(GroupName = "Xếp hạng học lực", Name = "Xóa thông tin Xếp hạng học lực")]
        XEP_HANG_HOC_LUC_DELETE,

        [Display(GroupName = "Xếp loại học tập thang 4", Name = "Xem thông tin Xếp loại học tập thang 4")]
        XEP_LOAI_HOC_TAP_THANG_4_VIEW,

        [Display(GroupName = "Xếp loại học tập thang 4", Name = "Thêm thông tin Xếp loại học tập thang 4")]
        XEP_LOAI_HOC_TAP_THANG_4_ADD,

        [Display(GroupName = "Xếp loại học tập thang 4", Name = "Sửa thông tin Xếp loại học tập thang 4")]
        XEP_LOAI_HOC_TAP_THANG_4_EDIT,

        [Display(GroupName = "Xếp loại học tập thang 4", Name = "Xóa thông tin Xếp loại học tập thang 4")]
        XEP_LOAI_HOC_TAP_THANG_4_DELETE,

        [Display(GroupName = "Xếp loại tốt nghiệp thang 4", Name = "Xem thông tin Xếp loại tốt nghiệp thang 4")]
        XEP_LOAI_TOT_NGHIEP_THANG_4_VIEW,

        [Display(GroupName = "Xếp loại tốt nghiệp thang 4", Name = "Thêm thông tin Xếp loại tốt nghiệp thang 4")]
        XEP_LOAI_TOT_NGHIEP_THANG_4_ADD,

        [Display(GroupName = "Xếp loại tốt nghiệp thang 4", Name = "Sửa thông tin Xếp loại tốt nghiệp thang 4")]
        XEP_LOAI_TOT_NGHIEP_THANG_4_EDIT,

        [Display(GroupName = "Xếp loại tốt nghiệp thang 4", Name = "Xóa thông tin Xếp loại tốt nghiệp thang 4")]
        XEP_LOAI_TOT_NGHIEP_THANG_4_DELETE,

        [Display(GroupName = "Xếp hạng tốt nghiệp thang điểm 10", Name = "Xem thông tin Xếp hạng tốt nghiệp thang điểm 10")]
        XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_VIEW,

        [Display(GroupName = "Xếp hạng tốt nghiệp thang điểm 10", Name = "Thêm thông tin Xếp hạng tốt nghiệp thang điểm 10")]
        XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_ADD,

        [Display(GroupName = "Xếp hạng tốt nghiệp thang điểm 10", Name = "Sửa thông tin Xếp hạng tốt nghiệp thang điểm 10")]
        XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_EDIT,

        [Display(GroupName = "Xếp hạng tốt nghiệp thang điểm 10", Name = "Xóa thông tin Xếp hạng tốt nghiệp thang điểm 10")]
        XEP_HANG_TOT_NGHIEP_THANG_DIEM_10_DELETE,

        [Display(GroupName = "Loại chúng chỉ", Name = "Xem thông tin Loại chúng chỉ")]
        LOAI_CHUNG_CHI_VIEW,

        [Display(GroupName = "Loại chúng chỉ", Name = "Thêm thông tin Loại chúng chỉ")]
        LOAI_CHUNG_CHI_ADD,

        [Display(GroupName = "Loại chúng chỉ", Name = "Sửa thông tin Loại chúng chỉ")]
        LOAI_CHUNG_CHI_EDIT,

        [Display(GroupName = "Loại chúng chỉ", Name = "Xóa thông tin Loại chúng chỉ")]
        LOAI_CHUNG_CHI_DELETE,

        [Display(GroupName = "Xếp loại chúng chỉ", Name = "Xem thông tin Xếp loại chúng chỉ")]
        XEP_LOAI_CHUNG_CHI_VIEW,

        [Display(GroupName = "Xếp loại chúng chỉ", Name = "Thêm thông tin Xếp loại chúng chỉ")]
        XEP_LOAI_CHUNG_CHI_ADD,

        [Display(GroupName = "Xếp loại chúng chỉ", Name = "Sửa thông tin Xếp loại chúng chỉ")]
        XEP_LOAI_CHUNG_CHI_EDIT,

        [Display(GroupName = "Xếp loại chúng chỉ", Name = "Xóa thông tin Xếp loại chúng chỉ")]
        XEP_LOAI_CHUNG_CHI_DELETE,

        [Display(GroupName = "Điểm rèn luyện quy đổi", Name = "Xem thông tin điểm rèn luyện quy đổi")]
        DIEM_REN_LUYEN_QUY_DOI_VIEW,

        [Display(GroupName = "Điểm rèn luyện quy đổi", Name = "Thêm thông tin điểm rèn luyện quy đổi")]
        DIEM_REN_LUYEN_QUY_DOI_ADD,

        [Display(GroupName = "Điểm rèn luyện quy đổi", Name = "Sửa thông tin điểm rèn luyện quy đổi")]
        DIEM_REN_LUYEN_QUY_DOI_EDIT,

        [Display(GroupName = "Điểm rèn luyện quy đổi", Name = "Xóa thông tin điểm rèn luyện quy đổi")]
        DIEM_REN_LUYEN_QUY_DOI_DELETE,

        [Display(GroupName = "Loại quyết định", Name = "Xem thông tin loại quyết định")]
        LOAI_QUYET_DINH_VIEW,

        [Display(GroupName = "Loại quyết định", Name = "Thêm thông tin loại quyết định")]
        LOAI_QUYET_DINH_ADD,

        [Display(GroupName = "Loại quyết định", Name = "Sửa thông tin loại quyết định")]
        LOAI_QUYET_DINH_EDIT,

        [Display(GroupName = "Loại quyết định", Name = "Xóa thông tin loại quyết định")]
        LOAI_QUYET_DINH_DELETE,

        [Display(GroupName = "Tòa nhà", Name = "Xem thông tin tòa nhà")]
        TOA_NHA_VIEW,

        [Display(GroupName = "Tòa nhà", Name = "Thêm thông tin tòa nhà")]
        TOA_NHA_ADD,

        [Display(GroupName = "Tòa nhà", Name = "Sửa thông tin tòa nhà")]
        TOA_NHA_EDIT,

        [Display(GroupName = "Tòa nhà", Name = "Xóa thông tin tòa nhà")]
        TOA_NHA_DELETE,



        [Display(GroupName = "Cơ sở đào tạo", Name = "Xem thông tin cơ sở đào tạo")]
        CO_SO_DAO_TAO_VIEW,

        [Display(GroupName = "Cơ sở đào tạo", Name = "Thêm thông tin cơ sở đào tạo")]
        CO_SO_DAO_TAO_ADD,

        [Display(GroupName = "Cơ sở đào tạo", Name = "Sửa thông tin cơ sở đào tạo")]
        CO_SO_DAO_TAO_EDIT,

        [Display(GroupName = "Cơ sở đào tạo", Name = "Xóa thông tin cơ sở đào tạo")]
        CO_SO_DAO_TAO_DELETE,
        
        [Display(GroupName = "Tham số hệ thống", Name = "Xem thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_VIEW,

        [Display(GroupName = "Tham số hệ thống", Name = "Thêm thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_ADD,

        [Display(GroupName = "Tham số hệ thống", Name = "Sửa thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_EDIT,

        [Display(GroupName = "Tham số hệ thống", Name = "Xóa thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_DELETE,

        [Display(GroupName = "Bộ môn", Name = "Xem thông tin bộ môn")]
        BO_MON_VIEW,

        [Display(GroupName = "Bộ môn", Name = "Thêm thông tin bộ môn")]
        BO_MON_ADD,

        [Display(GroupName = "Bộ môn", Name = "Sửa thông tin bộ môn")]
        BO_MON_EDIT,

        [Display(GroupName = "Bộ môn", Name = "Xóa thông tin bộ môn")]
        BO_MON_DELETE,


        [Display(GroupName = "Đối tượng học phí", Name = "Xem thông tin đối tượng học phí")]
        DOI_TUONG_HOC_PHI_VIEW,

        [Display(GroupName = "Đối tượng học phí", Name = "Thêm thông tin đối tượng học phí")]
        DOI_TUONG_HOC_PHI_ADD,

        [Display(GroupName = "Đối tượng học phí", Name = "Sửa thông tin đối tượng học phí")]
        DOI_TUONG_HOC_PHI_EDIT,

        [Display(GroupName = "Đối tượng học phí", Name = "Xóa thông tin đối tượng học phí")]
        DOI_TUONG_HOC_PHI_DELETE,


        [Display(GroupName = "Nhóm chứng chỉ", Name = "Xem thông tin nhóm chứng chỉ")]
        NHOM_CHUNG_CHI_VIEW,

        [Display(GroupName = "Nhóm chứng chỉ", Name = "Thêm thông tin nhóm chứng chỉ")]
        NHOM_CHUNG_CHI_ADD,

        [Display(GroupName = "Nhóm chứng chỉ", Name = "Sửa thông tin nhóm chứng chỉ")]
        NHOM_CHUNG_CHI_EDIT,

        [Display(GroupName = "Nhóm chứng chỉ", Name = "Xóa thông tin nhóm chứng chỉ")]
        NHOM_CHUNG_CHI_DELETE,




        [Display(GroupName = "Loại thu chi", Name = "Xem thông tin loại thu chi")]
        LOAI_THU_CHI_VIEW,

        [Display(GroupName = "Loại thu chi", Name = "Thêm thông tin loại thu chi")]
        LOAI_THU_CHI_ADD,

        [Display(GroupName = "Loại thu chi", Name = "Sửa thông tin loại thu chi")]
        LOAI_THU_CHI_EDIT,

        [Display(GroupName = "Loại thu chi", Name = "Xóa thông tin loại thu chi")]
        LOAI_THU_CHI_DELETE,



        [Display(GroupName = "Hình thức thi", Name = "Xem thông tin hình thức thi")]
        HINH_THUC_THI_VIEW,

        [Display(GroupName = "Hình thức thi", Name = "Thêm thông tin hình thức thi")]
        HINH_THUC_THI_ADD,

        [Display(GroupName = "Hình thức thi", Name = "Sửa thông tin hình thức thi")]
        HINH_THUC_THI_EDIT,

        [Display(GroupName = "Hình thức thi", Name = "Xóa thông tin hình thức thi")]
        HINH_THUC_THI_DELETE,


        [Display(GroupName = "Nơi thực tập", Name = "Xem thông tin nơi thực tập")]
        NOI_THUC_TAP_VIEW,

        [Display(GroupName = "Nơi thực tập", Name = "Thêm thông tin nơi thực tập")]
        NOI_THUC_TAP_ADD,

        [Display(GroupName = "Nơi thực tập", Name = "Sửa thông tin nơi thực tập")]
        NOI_THUC_TAP_EDIT,

        [Display(GroupName = "Nơi thực tập", Name = "Xóa thông tin nơi thực tập")]
        NOI_THUC_TAP_DELETE,



        [Display(GroupName = "Thành phần môn theo hệ", Name = "Xem thông tin thành phần môn theo hệ")]
        THANH_PHAN_MON_THEO_HE_VIEW,

        [Display(GroupName = "Thành phần môn theo hệ", Name = "Thêm thông tin thành phần môn theo hệ")]
        THANH_PHAN_MON_THEO_HE_ADD,

        [Display(GroupName = "Thành phần môn theo hệ", Name = "Sửa thông tin thành phần môn theo hệ")]
        THANH_PHAN_MON_THEO_HE_EDIT,

        [Display(GroupName = "Thành phần môn theo hệ", Name = "Xóa thông tin thành phần môn theo hệ")]
        THANH_PHAN_MON_THEO_HE_DELETE,



        [Display(GroupName = "Điểm quy đổi", Name = "Xem thông tin điểm quy đổi")]
        DIEM_QUY_DOI_VIEW,

        [Display(GroupName = "Điểm quy đổi", Name = "Thêm thông tin điểm quy đổi")]
        DIEM_QUY_DOI_ADD,

        [Display(GroupName = "Điểm quy đổi", Name = "Sửa thông tin điểm quy đổi")]
        DIEM_QUY_DOI_EDIT,

        [Display(GroupName = "Điểm quy đổi", Name = "Xóa thông tin điểm quy đổi")]
        DIEM_QUY_DOI_DELETE,



        [Display(GroupName = "Chương trình đào tạo kiến thức", Name = "Xem thông tin chương trình đào tạo kiến thức")]
        CHUONG_TRINH_DAO_TAO_KIEN_THUC_VIEW,

        [Display(GroupName = "Chương trình đào tạo kiến thức", Name = "Thêm thông tin chương trình đào tạo kiến thức")]
        CHUONG_TRINH_DAO_TAO_KIEN_THUC_ADD,

        [Display(GroupName = "Chương trình đào tạo kiến thức", Name = "Sửa thông tin chương trình đào tạo kiến thức")]
        CHUONG_TRINH_DAO_TAO_KIEN_THUC_EDIT,

        [Display(GroupName = "Chương trình đào tạo kiến thức", Name = "Xóa thông tin chương trình đào tạo kiến thức")]
        CHUONG_TRINH_DAO_TAO_KIEN_THUC_DELETE,



        [Display(GroupName = "Cấp rèn luyện", Name = "Xem thông tin cấp rèn luyện")]
        CAP_REN_LUYEN_VIEW,

        [Display(GroupName = "Cấp rèn luyện", Name = "Thêm thông tin cấp rèn luyện")]
        CAP_REN_LUYEN_ADD,

        [Display(GroupName = "Cấp rèn luyện", Name = "Sửa thông tin cấp rèn luyện")]
        CAP_REN_LUYEN_EDIT,

        [Display(GroupName = "Cấp rèn luyện", Name = "Xóa thông tin cấp rèn luyện")]
        CAP_REN_LUYEN_DELETE,


        [Display(GroupName = "Phòng", Name = "Xem thông tin phòng")]
        PHONG_VIEW,

        [Display(GroupName = "Phòng", Name = "Thêm thông tin phòng")]
        PHONG_ADD,

        [Display(GroupName = "Phòng", Name = "Sửa thông tin phòng")]
        PHONG_EDIT,

        [Display(GroupName = "Phòng", Name = "Xóa thông tin phòng")]
        PHONG_DELETE,



        [Display(GroupName = "Tầng", Name = "Xem thông tin tầng")]
        TANG_VIEW,

        [Display(GroupName = "Tầng", Name = "Thêm thông tin tầng")]
        TANG_ADD,

        [Display(GroupName = "Tầng", Name = "Sửa thông tin tầng")]
        TANG_EDIT,

        [Display(GroupName = "Tầng", Name = "Xóa thông tin tầng")]
        TANG_DELETE,


        [Display(GroupName = "Môn học", Name = "Xem thông tin môn học")]
        MON_HOC_VIEW,

        [Display(GroupName = "Môn học", Name = "Thêm thông tin môn học")]
        MON_HOC_ADD,

        [Display(GroupName = "Môn học", Name = "Sửa thông tin môn học")]
        MON_HOC_EDIT,

        [Display(GroupName = "Môn học", Name = "Xóa thông tin môn học")]
        MON_HOC_DELETE,


        [Display(GroupName = "Loại phòng", Name = "Xem thông tin loại phòng")]
        LOAI_PHONG_VIEW,

        [Display(GroupName = "Loại phòng", Name = "Thêm thông tin loại phòng")]
        LOAI_PHONG_ADD,

        [Display(GroupName = "Loại phòng", Name = "Sửa thông tin loại phòng")]
        LOAI_PHONG_EDIT,

        [Display(GroupName = "Loại phòng", Name = "Xóa thông tin loại phòng")]
        LOAI_PHONG_DELETE,

        [Display(GroupName = "Chương trình đào tạo", Name = "Xem thông tin chương trình đào tạo")]
        CHUONG_TRINH_DAO_TAO_VIEW,

        [Display(GroupName = "Chương trình đào tạo", Name = "Thêm thông tin chương trình đào tạo")]
        CHUONG_TRINH_DAO_TAO_ADD,

        [Display(GroupName = "Chương trình đào tạo", Name = "Sửa thông tin chương trình đào tạo")]
        CHUONG_TRINH_DAO_TAO_EDIT,

        [Display(GroupName = "Chương trình đào tạo", Name = "Xóa thông tin chương trình đào tạo")]
        CHUONG_TRINH_DAO_TAO_DELETE,

        [Display(GroupName = "Chương trình đào tạo ràng buộc", Name = "Xem thông tin chương trình đào tạo ràng buộc")]
        CHUONG_TRINH_DAO_TAO_RANG_BUOC_VIEW,

        [Display(GroupName = "Chương trình đào tạo ràng buộc", Name = "Thêm thông tin chương trình đào tạo ràng buộc")]
        CHUONG_TRINH_DAO_TAO_RANG_BUOC_ADD,

        [Display(GroupName = "Chương trình đào tạo ràng buộc", Name = "Sửa thông tin chương trình đào tạo ràng buộc")]
        CHUONG_TRINH_DAO_TAO_RANG_BUOC_EDIT,

        [Display(GroupName = "Chương trình đào tạo ràng buộc", Name = "Xóa thông tin chương trình đào tạo ràng buộc")]
        CHUONG_TRINH_DAO_TAO_RANG_BUOC_DELETE,

        [Display(GroupName = "Học phần tương đương", Name = "Xem thông tin học phần tương đương")]
        HOC_PHAN_TUONG_DUONG_VIEW,

        [Display(GroupName = "Học phần tương đương", Name = "Thêm thông tin học phần tương đương")]
        HOC_PHAN_TUONG_DUONG_ADD,

        [Display(GroupName = "Học phần tương đương", Name = "Sửa thông tin học phần tương đương")]
        HOC_PHAN_TUONG_DUONG_EDIT,

        [Display(GroupName = "Học phần tương đương", Name = "Xóa thông tin học phần tương đương")]
        HOC_PHAN_TUONG_DUONG_DELETE,

        
        [Display(GroupName = "Chương trình đào tạo chi tiết", Name = "Xem thông tin chương trình đào tạo chi tiết")]
        CHUONG_TRINH_DAO_TAO_CHI_TIET_VIEW,

        [Display(GroupName = "Chương trình đào tạo chi tiết", Name = "Thêm thông tin chương trình đào tạo chi tiết")]
        CHUONG_TRINH_DAO_TAO_CHI_TIET_ADD,

        [Display(GroupName = "Chương trình đào tạo chi tiết", Name = "Sửa thông tin chương trình đào tạo chi tiết")]
        CHUONG_TRINH_DAO_TAO_CHI_TIET_EDIT,

        [Display(GroupName = "Chương trình đào tạo chi tiết", Name = "Xóa thông tin chương trình đào tạo chi tiết")]
        CHUONG_TRINH_DAO_TAO_CHI_TIET_DELETE,

        [Display(GroupName = "Giáo án", Name = "Xem thông tin giáo án")]
        GIAO_AN_VIEW,

        [Display(GroupName = "Giáo án", Name = "Thêm thông tin giáo án")]
        GIAO_AN_ADD,

        [Display(GroupName = "Giáo án", Name = "Sửa thông tin giáo án")]
        GIAO_AN_EDIT,

        [Display(GroupName = "Giáo án", Name = "Xóa thông tin giáo án")]
        GIAO_AN_DELETE,


        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Xem thông tin chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_VIEW,

        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Thêm thông tin chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_ADD,

        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Sửa thông tin chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_EDIT,

        [Display(GroupName = "Chỉ tiêu tuyển sinh", Name = "Xóa thông tin chỉ tiêu tuyển sinh")]
        CHI_TIEU_TUYEN_SINH_DELETE,

        [Display(GroupName = "Mức hưởng BHYT", Name = "Thêm mức hưởng bhyt")]
        MUC_HUONG_BHYT_ADD,

        [Display(GroupName = "Mức hưởng BHYT", Name = "Sửa mức hưởng bhyt")]
        MUC_HUONG_BHYT_EDIT,

        [Display(GroupName = "Mức hưởng BHYT", Name = "Xóa mức hưởng bhyt")]
        MUC_HUONG_BHYT_DELETE,

        [Display(GroupName = "Mức hưởng BHYT", Name = "Xem thông tin mức hưởng bhyt")]
        MUC_HUONG_BHYT_VIEW,

        [Display(GroupName = "Vùng", Name = "Thêm vùng")]
        VUNG_ADD,

        [Display(GroupName = "Vùng", Name = "Sửa vùng")]
        VUNG_EDIT,

        [Display(GroupName = "Vùng", Name = "Xóa vùng")]
        VUNG_DELETE,

        [Display(GroupName = "Vùng", Name = "Xem thông tin vùng")]
        VUNG_VIEW,



        [Display(GroupName = "Bệnh viện", Name = "Xem thông tin bệnh viện")]
        BENH_VIEN_VIEW,

        [Display(GroupName = "Bệnh viện", Name = "Thêm thông tin bệnh viện")]
        BENH_VIEN_ADD,

        [Display(GroupName = "Bệnh viện", Name = "Sửa thông tin bệnh viện")]
        BENH_VIEN_EDIT,

        [Display(GroupName = "Bệnh viện", Name = "Xóa thông tin bệnh viện")]
        BENH_VIEN_DELETE,



        [Display(GroupName = "Phương án", Name = "Xem thông tin phương án")]
        PHUONG_AN_VIEW,

        [Display(GroupName = "Phương án", Name = "Thêm thông tin phương án")]
        PHUONG_AN_ADD,

        [Display(GroupName = "Phương án", Name = "Sửa thông tin phương án")]
        PHUONG_AN_EDIT,

        [Display(GroupName = "Phương án", Name = "Xóa thông tin phương án")]
        PHUONG_AN_DELETE,

        [Display(GroupName = "Bậc đào tạo", Name = "Xem thông tin bậc đào tạo")]
        BAC_DAO_TAO_VIEW,

        [Display(GroupName = "Bậc đào tạo", Name = "Thêm thông tin bậc đào tạo")]
        BAC_DAO_TAO_ADD,

        [Display(GroupName = "Bậc đào tạo", Name = "Sửa thông tin bậc đào tạo")]
        BAC_DAO_TAO_EDIT,

        [Display(GroupName = "Bậc đào tạo", Name = "Xóa thông tin bậc đào tạo")]
        BAC_DAO_TAO_DELETE,

        [Display(GroupName = "Phương thức đóng", Name = "Xem thông tin Phương thức đóng")]
        PHUONG_THUC_DONG_VIEW,

        [Display(GroupName = "Phương thức đóng", Name = "Thêm thông tin Phương thức đóng")]
        PHUONG_THUC_DONG_ADD,

        [Display(GroupName = "Phương thức đóng", Name = "Sửa thông tin Phương thức đóng")]
        PHUONG_THUC_DONG_EDIT,

        [Display(GroupName = "Phương thức đóng", Name = "Xóa thông tin Phương thức đóng")]
        PHUONG_THUC_DONG_DELETE,

        [Display(GroupName = "Quy trình phê duyệt", Name = "Gán chức năng với quy trình")]
        GAN_QUY_TRINH_CHUC_NANG
    }
}
