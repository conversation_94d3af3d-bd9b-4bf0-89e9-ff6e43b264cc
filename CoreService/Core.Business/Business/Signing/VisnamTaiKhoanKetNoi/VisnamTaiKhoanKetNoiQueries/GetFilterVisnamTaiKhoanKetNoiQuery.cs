using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetFilterVisnamTaiKhoanKetNoiQuery : IRequest<PaginationList<VisnamTaiKhoanKetNoiBaseModel>>
    {
        public VisnamTaiKhoanKetNoiQueryFilter Filter { get; set; }

        /// <summary>
        /// Lấy danh sách tài khoản kết nối Visnam theo điều kiện lọc
        /// </summary>
        /// <param name="filter">Thông tin lọc</param>
        public GetFilterVisnamTaiKhoanKetNoiQuery(VisnamTaiKhoanKetNoiQueryFilter filter)
        {
            Filter = filter;
        }

        public class Handler : IRequestHandler<GetFilterVisnamTaiKhoanKetNoiQuery, PaginationList<VisnamTaiKhoanKetNoiBaseModel>>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<PaginationList<VisnamTaiKhoanKetNoiBaseModel>> Handle(GetFilterVisnamTaiKhoanKetNoiQuery request, CancellationToken cancellationToken)
            {
                var filter = request.Filter;

                var data = (from dt in _dataContext.SgVisnamTaiKhoanKetNois.AsNoTracking()
                            join user in _dataContext.Users.AsNoTracking() on dt.UserId equals user.Id into userGroup
                            from user in userGroup.DefaultIfEmpty()
                            select new { dt, user });

                if (!string.IsNullOrEmpty(filter.TextSearch))
                {
                    string ts = filter.TextSearch.Trim().ToLower();
                    data = data.Where(x => x.dt.Key.ToLower().Contains(ts) || 
                                          (x.user != null && x.user.FullName.ToLower().Contains(ts)) ||
                                          (x.user != null && x.user.UserName.ToLower().Contains(ts)));
                }

                if (filter.IsActive.HasValue)
                {
                    data = data.Where(x => x.dt.IsActive == filter.IsActive);
                }

                if (filter.UserId.HasValue)
                {
                    data = data.Where(x => x.dt.UserId == filter.UserId);
                }

                if (filter.CreatedDateFrom.HasValue)
                {
                    data = data.Where(x => x.dt.CreatedDate >= filter.CreatedDateFrom);
                }

                if (filter.CreatedDateTo.HasValue)
                {
                    data = data.Where(x => x.dt.CreatedDate <= filter.CreatedDateTo);
                }

                var orderedData = data.OrderByField($"dt.{filter.PropertyName}", filter.Ascending);

                // Apply pagination with improved performance
                if (filter.PageSize <= 0)
                {
                    filter.PageSize = QueryFilter.DefaultPageSize;
                }

                // Get total count asynchronously
                int totalCount = await orderedData.CountAsync();

                // Calculate number of rows to skip on pagesize
                int excludedRows = (filter.PageNumber - 1) * filter.PageSize;
                if (excludedRows <= 0)
                {
                    excludedRows = 0;
                }

                // Apply pagination and get data asynchronously
                var listData = await orderedData
                    .Skip(excludedRows)
                    .Take(filter.PageSize)
                    .Select(x => new VisnamTaiKhoanKetNoiBaseModel
                    {
                        Id = x.dt.Id,
                        UserId = x.dt.UserId,
                        Key = x.dt.Key,
                        Secret = MaskSecret(x.dt.Secret), // Mask secret để bảo mật
                        IsActive = x.dt.IsActive,
                        Order = x.dt.Order,
                        CreatedDate = x.dt.CreatedDate,
                        UserName = x.user != null ? x.user.FullName : ""
                    })
                    .ToListAsync();

                return new PaginationList<VisnamTaiKhoanKetNoiBaseModel>()
                {
                    DataCount = listData.Count,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize,
                    Data = listData
                };
            }

            private string MaskSecret(string secret)
            {
                if (string.IsNullOrEmpty(secret))
                    return "";

                if (secret.Length <= 8)
                    return new string('*', secret.Length);

                return secret.Substring(0, 4) + new string('*', secret.Length - 8) + secret.Substring(secret.Length - 4);
            }
        }
    }
}
