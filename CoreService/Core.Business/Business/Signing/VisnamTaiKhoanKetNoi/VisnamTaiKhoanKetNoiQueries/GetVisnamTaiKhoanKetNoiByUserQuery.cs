using Core.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetVisnamTaiKhoanKetNoiByUserQuery : IRequest<VisnamTaiKhoanKetNoiModel>
    {
        public int UserId { get; set; }

        /// <summary>
        /// Lấy thông tin tài khoản kết nối Visnam theo User
        /// </summary>
        /// <param name="userId">Id người dùng</param>
        public GetVisnamTaiKhoanKetNoiByUserQuery(int userId)
        {
            UserId = userId;
        }

        public class Handler : IRequestHandler<GetVisnamTaiKhoanKetNoiByUserQuery, VisnamTaiKhoanKetNoiModel>
        {
            private readonly SystemReadDataContext _dataContext;

            public Handler(SystemReadDataContext dataContext)
            {
                _dataContext = dataContext;
            }

            public async Task<VisnamTaiKhoanKetNoiModel> Handle(GetVisnamTaiKhoanKetNoiByUserQuery request, CancellationToken cancellationToken)
            {
                var userId = request.UserId;

                var entity = await _dataContext.SgVisnamTaiKhoanKetNois.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.UserId == userId && x.IsActive);

                if (entity == null)
                    return null;

                var result = new VisnamTaiKhoanKetNoiModel
                {
                    Id = entity.Id,
                    UserId = entity.UserId,
                    Key = entity.Key,
                    Secret = entity.Secret,
                    IsActive = entity.IsActive,
                    Order = entity.Order,
                    CreatedDate = entity.CreatedDate
                };

                return result;
            }
        }
    }
}
