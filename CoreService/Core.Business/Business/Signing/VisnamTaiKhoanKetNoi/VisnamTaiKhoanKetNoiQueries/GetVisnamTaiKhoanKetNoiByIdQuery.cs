using Core.Data;
using Core.Data.Signing;
using Core.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetVisnamTaiKhoanKetNoiByIdQuery : IRequest<VisnamTaiKhoanKetNoiModel>
    {
        public int Id { get; set; }

        /// <summary>
        /// Lấy thông tin tài khoản kết nối Visnam theo Id
        /// </summary>
        /// <param name="id">Id tài khoản kết nối Visnam</param>
        public GetVisnamTaiKhoanKetNoiByIdQuery(int id)
        {
            Id = id;
        }

        public class Handler : IRequestHandler<GetVisnamTaiKhoanKetNoiByIdQuery, VisnamTaiKhoanKetNoiModel>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<VisnamTaiKhoanKetNoiModel> Handle(GetVisnamTaiKhoanKetNoiByIdQuery request, CancellationToken cancellationToken)
            {
                var id = request.Id;
                string cacheKey = VisnamTaiKhoanKetNoiConstant.BuildCacheKey(id.ToString());
                var item = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var entity = await _dataContext.SgVisnamTaiKhoanKetNois.AsNoTracking().FirstOrDefaultAsync(x => x.Id == id);

                    return AutoMapperUtils.AutoMap<SgVisnamTaiKhoanKetNoi, VisnamTaiKhoanKetNoiModel>(entity);
                });
                return item;
            }
        }
    }
}
