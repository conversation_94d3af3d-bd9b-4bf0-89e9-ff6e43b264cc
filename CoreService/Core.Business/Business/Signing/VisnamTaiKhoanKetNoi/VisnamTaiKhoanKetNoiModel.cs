using Core.Data.Signing;
using Core.Shared;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class VisnamTaiKhoanKetNoiBaseModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "visnam-tai-khoan-ket-noi.user-id.required")]
        public int UserId { get; set; }

        [Required(ErrorMessage = "visnam-tai-khoan-ket-noi.key.required")]
        [MaxLength(500, ErrorMessage = "visnam-tai-khoan-ket-noi.key.max-length")]
        public string Key { get; set; }

        [Required(ErrorMessage = "visnam-tai-khoan-ket-noi.secret.required")]
        [MaxLength(500, ErrorMessage = "visnam-tai-khoan-ket-noi.secret.max-length")]
        public string Secret { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }

        public DateTime? CreatedDate { get; set; }

        public string UserName { get; set; }
    }

    public class VisnamTaiKhoanKetNoiModel : VisnamTaiKhoanKetNoiBaseModel
    {
        // Model đầy đủ thông tin (hiện tại giống BaseModel vì entity đơn giản)
    }

    public class CreateVisnamTaiKhoanKetNoiModel : VisnamTaiKhoanKetNoiModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateVisnamTaiKhoanKetNoiModel : VisnamTaiKhoanKetNoiModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(SgVisnamTaiKhoanKetNoi entity)
        {
            entity.UserId = this.UserId;
            entity.Key = this.Key;
            entity.Secret = this.Secret;
            entity.IsActive = this.IsActive;
            entity.Order = this.Order;
            entity.ModifiedDate = DateTime.Now;
            entity.ModifiedUserId = this.ModifiedUserId;
        }
    }

    public class VisnamTaiKhoanKetNoiSelectItemModel : SelectItemModel
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string Key { get; set; }
        public string MaskedSecret { get; set; } // Secret được mask để bảo mật
    }

    public class VisnamTaiKhoanKetNoiQueryFilter : BaseQueryFilterModel
    {
        public int? UserId { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
    }
}
