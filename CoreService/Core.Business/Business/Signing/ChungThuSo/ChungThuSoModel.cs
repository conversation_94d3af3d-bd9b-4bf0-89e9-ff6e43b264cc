using Core.Data.Signing;
using Core.Shared;
using System;
using System.ComponentModel.DataAnnotations;

namespace Core.Business
{
    public class ChungThuSoBaseModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "chung-thu-so.serial-number.required")]
        [MaxLength(100, ErrorMessage = "chung-thu-so.serial-number.max-length")]
        public string SerialNumber { get; set; }

        [Required(ErrorMessage = "chung-thu-so.subject-name.required")]
        [MaxLength(500, ErrorMessage = "chung-thu-so.subject-name.max-length")]
        public string SubjectName { get; set; }

        [Required(ErrorMessage = "chung-thu-so.issuer.required")]
        [MaxLength(50, ErrorMessage = "chung-thu-so.issuer.max-length")]
        public string Issuer { get; set; }

        public DateTime NotBefore { get; set; }

        public DateTime NotAfter { get; set; }

        public short Source { get; set; }

        public bool IsActive { get; set; } = true;

        public int Order { get; set; }

        public DateTime? CreatedDate { get; set; }
    }

    public class ChungThuSoModel : ChungThuSoBaseModel
    {
        [Required(ErrorMessage = "chung-thu-so.certificate-base64.required")]
        public string CertificateBase64 { get; set; }
    }

    public class CreateChungThuSoModel : ChungThuSoModel
    {
        public int? CreatedUserId { get; set; }
    }

    public class UpdateChungThuSoModel : ChungThuSoModel
    {
        public int? ModifiedUserId { get; set; }

        public void UpdateEntity(SgChungThuSo entity)
        {
            entity.SerialNumber = this.SerialNumber;
            entity.CertificateBase64 = this.CertificateBase64;
            entity.SubjectName = this.SubjectName;
            entity.Issuer = this.Issuer;
            entity.NotBefore = this.NotBefore;
            entity.NotAfter = this.NotAfter;
            entity.Source = this.Source;
            entity.IsActive = this.IsActive;
            entity.Order = this.Order;
            entity.ModifiedDate = DateTime.Now;
            entity.ModifiedUserId = this.ModifiedUserId;
        }
    }

    public class ChungThuSoSelectItemModel : SelectItemModel
    {
        public string SerialNumber { get; set; }
        public string SubjectName { get; set; }
        public DateTime NotAfter { get; set; }
        public short Source { get; set; }
    }

    public class ChungThuSoQueryFilter : BaseQueryFilterModel
    {
        public short? Source { get; set; }
        public DateTime? NotBeforeFrom { get; set; }
        public DateTime? NotBeforeTo { get; set; }
        public DateTime? NotAfterFrom { get; set; }
        public DateTime? NotAfterTo { get; set; }
        public bool? IsExpired { get; set; }
    }
}
