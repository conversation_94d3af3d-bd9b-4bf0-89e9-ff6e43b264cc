using Core.Data;
using Core.Shared.Enums;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Core.Business
{
    public class GetComboboxMauChuKyQuery : IRequest<List<MauChuKySelectItemModel>>
    {
        public int Count { get; set; }
        public string TextSearch { get; set; }
        public int? UserId { get; set; }
        public short? LoaiKySuDung { get; set; }

        /// <summary>
        /// Lấy danh sách mẫu chữ ký cho combobox
        /// </summary>
        /// <param name="count">Số lượng bản ghi cần lấy ra</param>
        /// <param name="textSearch">Từ khóa tìm kiếm</param>
        public GetComboboxMauChuKyQuery(int count = 0, string textSearch = "", int? userId = null,
            short? loaiKySuDung = null)
        {
            this.Count = count;
            this.TextSearch = textSearch;
            this.UserId = userId;
            this.LoaiKySuDung = loaiKySuDung;
        }

        public class Handler : IRequestHandler<GetComboboxMauChuKyQuery, List<MauChuKySelectItemModel>>
        {
            private readonly SystemReadDataContext _dataContext;
            private readonly ICacheService _cacheService;

            public Handler(SystemReadDataContext dataContext, ICacheService cacheService)
            {
                _dataContext = dataContext;
                _cacheService = cacheService;
            }

            public async Task<List<MauChuKySelectItemModel>> Handle(GetComboboxMauChuKyQuery request,
                CancellationToken cancellationToken)
            {
                var count = request.Count;
                var textSearch = request.TextSearch;
                var userId = request.UserId;

                string cacheKey = MauChuKyConstant.BuildCacheKey();
                var list = await _cacheService.GetOrCreate(cacheKey, async () =>
                {
                    var data = (from item in _dataContext.SgMauChuKys.AsNoTracking().Where(x => x.IsActive)
                        orderby item.Order, item.Name
                        select new MauChuKySelectItemModel()
                        {
                            Id = item.Id,
                            Code = item.Code,
                            Name = item.Name,
                            UserId = item.UserId,
                            LoaiKySuDung = item.LoaiKySuDung,
                            LoaiKySuDungText = GetLoaiKySuDungText(item.LoaiKySuDung)
                        });

                    return await data.ToListAsync();
                });

                if (!string.IsNullOrEmpty(textSearch))
                {
                    string ts = textSearch.Trim().ToLower();
                    list = list.Where(x => x.Code.ToLower().Contains(ts) ||
                                           x.Name.ToLower().Contains(ts)).ToList();
                }

                if (userId.HasValue)
                {
                    list = list.Where(x => x.UserId == userId.Value).ToList();
                }

                if (request.LoaiKySuDung.HasValue)
                {
                    list = list.Where(x => x.LoaiKySuDung == request.LoaiKySuDung.Value).ToList();
                }

                if (count > 0)
                {
                    list = list.Take(count).ToList();
                }

                return list;
            }

            private string GetLoaiKySuDungText(short loaiKySuDung)
            {
                return loaiKySuDung switch
                {
                    (short)LoaiKyEnum.KyNhay => "Ký nháy",
                    (short)LoaiKyEnum.KyChinh => "Ký chính",
                    (short)LoaiKyEnum.KyDongDau => "Ký đóng dấu",
                    _ => "Không xác định"
                };
            }
        }
    }
}