// using Core.Data;
// using Core.Shared.Enums;
// using MediatR;
// using Microsoft.EntityFrameworkCore;
// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Text;
// using System.Threading;
// using System.Threading.Tasks;
//
// namespace Core.Business
// {
//     public class GetMauChuKyByUserQuery : IRequest<List<MauChuKySelectItemModel>>
//     {
//         public int UserId { get; set; }
//         public short? LoaiKySuDung { get; set; }
//
//         /// <summary>
//         /// L<PERSON><PERSON> danh sách mẫu chữ ký theo User
//         /// </summary>
//         /// <param name="userId">Id người dùng</param>
//         /// <param name="loaiKySuDung">Loại ký sử dụng (optional)</param>
//         public GetMauChuKyByUserQuery(int userId, short? loaiKySuDung = null)
//         {
//             UserId = userId;
//             LoaiKySuDung = loaiKySuDung;
//         }
//
//         public class Handler : IRequestHandler<GetMauChuKyByUserQuery, List<MauChuKySelectItemModel>>
//         {
//             private readonly SystemReadDataContext _dataContext;
//
//             public Handler(SystemReadDataContext dataContext)
//             {
//                 _dataContext = dataContext;
//             }
//
//             public async Task<List<MauChuKySelectItemModel>> Handle(GetMauChuKyByUserQuery request, CancellationToken cancellationToken)
//             {
//                 var userId = request.UserId;
//                 var loaiKySuDung = request.LoaiKySuDung;
//
//                 var query = _dataContext.SgMauChuKys.AsNoTracking()
//                     .Where(x => x.UserId == userId && x.IsActive);
//
//                 if (loaiKySuDung.HasValue)
//                 {
//                     query = query.Where(x => x.LoaiKySuDung == loaiKySuDung);
//                 }
//
//                 var data = await query
//                     .OrderBy(x => x.Order)
//                     .ThenBy(x => x.Name)
//                     .Select(item => new MauChuKySelectItemModel()
//                     {
//                         Id = item.Id,
//                         Code = item.Code,
//                         Name = item.Name,
//                         UserId = item.UserId,
//                         LoaiKySuDung = item.LoaiKySuDung,
//                         LoaiKySuDungText = GetLoaiKySuDungText(item.LoaiKySuDung)
//                     })
//                     .ToListAsync();
//
//                 return data;
//             }
//
//             private string GetLoaiKySuDungText(short loaiKySuDung)
//             {
//                 return loaiKySuDung switch
//                 {
//                     (short)LoaiKyEnum.KyNhay => "Ký nháy",
//                     (short)LoaiKyEnum.KyChinh => "Ký chính",
//                     (short)LoaiKyEnum.KyDongDau => "Ký đóng dấu",
//                     _ => "Không xác định"
//                 };
//             }
//         }
//     }
// }
