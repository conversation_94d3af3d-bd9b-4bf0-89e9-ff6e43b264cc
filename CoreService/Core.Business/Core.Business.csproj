<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Core\LayerSign\**" />
    <Compile Remove="Core\PDFToImage\**" />
    <Compile Remove="lib\**" />
    <Compile Remove="System\ApiCatalog\**" />
    <EmbeddedResource Remove="Core\LayerSign\**" />
    <EmbeddedResource Remove="Core\PDFToImage\**" />
    <EmbeddedResource Remove="lib\**" />
    <EmbeddedResource Remove="System\ApiCatalog\**" />
    <None Remove="Core\LayerSign\**" />
    <None Remove="Core\PDFToImage\**" />
    <None Remove="lib\**" />
    <None Remove="System\ApiCatalog\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="FluentEmail.Core" Version="3.0.2" />
    <PackageReference Include="FluentEmail.Liquid" Version="3.0.2" />
    <PackageReference Include="FluentEmail.Razor" Version="3.0.2" />
    <PackageReference Include="FluentEmail.Smtp" Version="3.0.2" />
    <PackageReference Include="MimeMapping" Version="3.0.1" />
    <PackageReference Include="Minio.AspNetCore" Version="4.0.6" />
    <PackageReference Include="Novell.Directory.Ldap.NETStandard" Version="3.6.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.2" />
    <PackageReference Include="System.DirectoryServices" Version="8.0.0" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="8.0.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Core.DataLog\Core.DataLog.csproj" />
    <ProjectReference Include="..\Core.Data\Core.Data.csproj" />
    <ProjectReference Include="..\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Business\Signing\VisnamTaiKhoanKetNoi\" />
  </ItemGroup>
</Project>