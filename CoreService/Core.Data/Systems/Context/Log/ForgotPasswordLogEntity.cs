using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Data
{
    [Table("log_forgot_password")]
    public class ForgotPasswordLogEntity
    {
        [Key]
        [Column("id", Order = 1)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Column("trace_id")]
        public string TraceId { get; set; }

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("user_name")]
        public string UserName { get; set; }

        [Column("user_email")]
        public string UserEmail { get; set; }

        [Column("token")]
        public string Token { get; set; }

        //[Column("secret_key")]
        //public string SecretKey { get; set; }

        [Column("is_updated_password")]
        public bool IsUpdatedPassword { get; set; }

        [Column("created_date")]
        public DateTime CreatedDate { get; set; }

        [Column("modifited_date")]
        public DateTime? ModifiedDate { get; set; }

        [Column("expire_time")]
        public DateTime ExpireTime { get; set; }
    }
}
