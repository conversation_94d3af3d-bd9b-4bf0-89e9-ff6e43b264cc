using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace Core.Data
{
    [Table("sys_chung_thu_so")]
    public class ChungThuSo : BaseTableDefault
    {
        public ChungThuSo()
        {
        }

        [Column("code")]
        public string Code { get; set; }

        [Column("name")]
        public string Name { get; set; }

        [Column("serial_number")]
        public string SerialNumber { get; set; }

        [Column("issuer")]
        public string Issuer { get; set; }

        [Column("subject")]
        public string Subject { get; set; }

        [Column("valid_from")]
        public DateTime? ValidFrom { get; set; }

        [Column("valid_to")]
        public DateTime? ValidTo { get; set; }

        [Column("certificate_type")]
        public string CertificateType { get; set; }

        [Column("key_usage")]
        public string KeyUsage { get; set; }

        [Column("thumbprint")]
        public string Thumbprint { get; set; }

        [Column("public_key")]
        public string PublicKey { get; set; }

        [Column("certificate_data")]
        public string CertificateData { get; set; }

        [Column("is_expired")]
        public bool IsExpired { get; set; }

        [Column("owner_name")]
        public string OwnerName { get; set; }

        [Column("owner_email")]
        public string OwnerEmail { get; set; }

        [Column("owner_organization")]
        public string OwnerOrganization { get; set; }

        [Column("status")]
        public int Status { get; set; } = 1; // 1: Active, 2: Inactive, 3: Revoked, 4: Expired

        [Column("notes")]
        public string Notes { get; set; }

        [Column("extensions")]
        public string ExtensionsJson
        {
            get
            {
                return Extensions == null ? null : JsonSerializer.Serialize(Extensions);
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    Extensions = null;
                else
                    Extensions = JsonSerializer.Deserialize<Dictionary<string, string>>(value);
            }
        }

        [NotMapped]
        public Dictionary<string, string> Extensions { get; set; }
    }
}
