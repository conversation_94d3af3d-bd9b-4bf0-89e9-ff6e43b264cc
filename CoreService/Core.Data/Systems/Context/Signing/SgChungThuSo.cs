using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Core.Shared;

namespace Core.Data.Signing;

[Table("sgChungThuSo")]
public class SgChungThuSo : BaseEntity
{
    [Column("user_id")]
    [Required]
    public int UserId { get; set; }
    
    /// <summary>
    /// Serial number của chứng thư số
    /// </summary>
    [Column("serial_number")]
    [Required]
    [MaxLength(100)]
    public string SerialNumber { get; set; }
    
    /// <summary>
    /// Chứng thư số định dạng base64
    /// </summary>
    [Column("certificate_base64")]
    [Required]
    public string CertificateBase64 { get; set; }
    
    /// <summary>
    /// SubjectName của chứng thư số
    /// </summary>
    [Column("subject_name")]
    [Required]
    [MaxLength(500)]
    public string SubjectName { get; set; }
    
    /// <summary>
    /// Issuer của chứng thư số
    /// </summary>
    [Column("issuer")]
    [Required]
    [MaxLength(50)]
    public string Issuer { get; set; }
    
    /// <summary>
    /// Ngày bắt đầu hiệu lực của chứng thư số
    /// </summary>
    [Column("not_before")]
    [Required]
    public DateTime NotBefore { get; set; }
    
    /// <summary>
    /// Ngày hết hiệu lực của chứng thư số
    /// </summary>
    [Column("not_after")]
    [Required]
    public DateTime NotAfter { get; set; }

    /// <summary>
    /// Đơn vị cung cấp chứng thư số - DoiTacKySoEnum
    /// </summary>
    public short Source { get; set; }
}