using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using Core.Shared;
using Core.Shared.Model;

namespace Core.Data.Signing;

[Table("sgMauChuKy")]
public class SgMauChuKy : BaseEntity
{
    JsonSerializerOptions jso = new JsonSerializerOptions();
    
    /// <summary>
    /// Mã chữ ký
    /// </summary>
    [Column("code")]
    [Required]
    [MaxLength(50)]
    public string Code { get; set; }

    /// <summary>
    /// Tên chữ ký
    /// </summary>
    [Column("name")]
    [Required]
    [MaxLength(100)]
    public string Name { get; set; }
    
    /// <summary>
    /// Hình ảnh hiển thị chữ ký
    /// </summary>
    [Column("image_base64")]
    [DataType(DataType.Text)]
    public string ImageBase64 { get; set; }

    /// <summary>
    /// Cấu hình hiển thị chữ ký số (dùng để cấu hình hiển thị các thông tin trong chứng thư số lên chữ ký)
    /// </summary>
    [Column("cau_hinh_hien_thi_chu_ky_json")]
    public string CauHinhHienThiChuKyJson
    {
        get
        {
            return CauHinhHienThiChuKy == null ? null : JsonSerializer.Serialize(CauHinhHienThiChuKy, jso);
        }
        set
        {
            if (string.IsNullOrWhiteSpace(value))
                CauHinhHienThiChuKy = null;
            else
                CauHinhHienThiChuKy = JsonSerializer.Deserialize<CauHinhHienThiChuKyModel>(value);
        }
    }

    [NotMapped]
    public CauHinhHienThiChuKyModel CauHinhHienThiChuKy { get; set; }


    /// <summary>
    /// Loại ký sử dụng - LoaiKyEnum: Ký nháy, ký chính, ký đóng dấu
    /// </summary>
    public short LoaiKySuDung { get; set; }
}